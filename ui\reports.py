from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QTableWidget, QTableWidgetItem,
                            QHeaderView, QTabWidget, QDateEdit, QComboBox,
                            QGroupBox, QFormLayout, QTextBrowser, QFileDialog,
                            QMessageBox, QFrame, QSizePolicy, QMenu, QAction)
from PyQt5.QtCore import QDate, Qt
from PyQt5.QtGui import QColor, QFont
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
import csv
import json

from database import Sale, Purchase, SaleItem, Inventory, Client, Supplier, Expense, Revenue, Project
from utils import show_error_message, qdate_to_datetime, format_currency
from ui.unified_styles import StyledLabel

class ReportsWidget(QWidget):
    """واجهة التقارير والتحليل المالي"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق تماماً لباقي البرنامج
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش الخارجية
        main_layout.setSpacing(3)  # تقليل المسافات بين العناصر

        # إضافة العنوان الرئيسي مطابق للفواتير
        title_label = QLabel("📊 إدارة التقارير المتطورة - نظام شامل ومتقدم لإدارة التقارير مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إزالة الإطار العلوي والاكتفاء بالتبويبات فقط

        # إنشاء تبويبات للتقارير المختلفة مع تصميم محسن ومطابق لباقي البرنامج
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 3px solid #000000;
                border-radius: 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                margin-top: 2px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border: 4px solid #000000;
                border-bottom: 4px solid #000000;
                border-radius: 12px;
                padding: 8px 32px;
                margin: 2px;
                font-size: 20px;
                font-weight: bold;
                min-width: 133px;
                max-width: 133px;
                min-height: 30px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2),
                           0 1px 3px rgba(0, 0, 0, 0.1),
                           0 -2px 5px rgba(0, 0, 0, 0.1);
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 6px solid #2563EB;
                border-bottom: 6px solid #2563EB;
                margin-top: -1px;
                padding: 9px 32px;
                font-size: 20px;
                font-weight: bold;
                min-width: 140px;
                max-width: 140px;
                min-height: 30px;
                max-height: 40px;
                text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
                box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4),
                           0 3px 12px rgba(0, 0, 0, 0.3),
                           0 -3px 8px rgba(37, 99, 235, 0.3);
                border-radius: 12px;
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569);
                border: 4px solid #3B82F6;
                border-bottom: 4px solid #3B82F6;
                color: #ffffff;
                min-width: 150px;
                max-width: 150px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.45);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.35),
                           0 2px 6px rgba(0, 0, 0, 0.2),
                           0 -2px 5px rgba(59, 130, 246, 0.25);
                border-radius: 12px;
            }
        """)

        # تبويب تقرير العملاء
        self.clients_tab = QWidget()
        self.setup_clients_tab()
        self.tabs.addTab(self.clients_tab, "🤝 العملاء")

        # تبويب تقرير الموردين
        self.suppliers_tab = QWidget()
        self.setup_suppliers_tab()
        self.tabs.addTab(self.suppliers_tab, "🏭 الموردين")

        # تبويب تقرير المشاريع
        self.projects_tab = QWidget()
        self.setup_projects_tab()
        self.tabs.addTab(self.projects_tab, "🏗️ المشاريع")

        # تبويب تقرير الإيرادات
        self.revenues_tab = QWidget()
        self.setup_revenues_tab()
        self.tabs.addTab(self.revenues_tab, "💰 الإيرادات")

        # تبويب تقرير المصروفات
        self.expenses_tab = QWidget()
        self.setup_expenses_tab()
        self.tabs.addTab(self.expenses_tab, "💸 المصروفات")

        # تبويب تقرير المشتريات
        self.purchases_tab = QWidget()
        self.setup_purchases_tab()
        self.tabs.addTab(self.purchases_tab, "🛒 المشتريات")

        # تبويب تقرير المبيعات
        self.sales_tab = QWidget()
        self.setup_sales_tab()
        self.tabs.addTab(self.sales_tab, "📊 المبيعات")

        # تبويب تقرير المخزون
        self.inventory_tab = QWidget()
        self.setup_inventory_tab()
        self.tabs.addTab(self.inventory_tab, "📦 المخزن")

        # تبويب تقرير الأرباح والخسائر
        self.profit_loss_tab = QWidget()
        self.setup_profit_loss_tab()
        self.tabs.addTab(self.profit_loss_tab, "💰 الأرباح والخسائر")

        main_layout.addWidget(self.tabs, 1)  # إعطاء التبويبات أولوية في التمدد
        self.setLayout(main_layout)

    def setup_sales_tab(self):
        """إعداد تبويب تقرير المبيعات"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير مع تصميم محسن
        filter_group = QGroupBox("🔍 فلاتر التقرير")
        filter_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 3px solid #3498db;
                border-radius: 15px;
                margin-top: 10px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e3f2fd,
                    stop:1 #bbdefb);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 5px 15px;
                background-color: #3498db;
                color: white;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        filter_layout = QFormLayout()
        filter_layout.setSpacing(12)
        filter_layout.setContentsMargins(20, 25, 20, 15)

        # فلتر الفترة الزمنية مع تصميم محسن
        period_label = QLabel("📅 الفترة الزمنية:")
        period_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)

        self.sales_period_combo = QComboBox()
        self.sales_period_combo.addItems(["اليوم", "الأسبوع الحالي", "الشهر الحالي", "الربع الحالي", "السنة الحالية", "فترة محددة"])
        self.sales_period_combo.currentIndexChanged.connect(self.on_sales_period_changed)
        # تطبيق التصميم المتطور الموحد
        self.style_advanced_combobox(self.sales_period_combo, 'primary')
        filter_layout.addRow(period_label, self.sales_period_combo)

        # حقول تاريخ البداية والنهاية مع تصميم محسن
        date_layout = QHBoxLayout()
        date_layout.setSpacing(15)

        # تسمية وحقل تاريخ البداية
        start_label = QLabel("📅 من:")
        start_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                font-weight: bold;
                color: #27ae60;
                padding: 5px;
            }
        """)

        self.sales_start_date = QDateEdit()
        self.sales_start_date.setCalendarPopup(True)
        self.sales_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.sales_start_date.setEnabled(False)
        self.sales_start_date.setStyleSheet("""
            QDateEdit {
                border: 2px solid #27ae60;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QDateEdit:hover {
                border: 2px solid #229954;
                background-color: #f8f9fa;
            }
            QDateEdit:disabled {
                background-color: #ecf0f1;
                color: #7f8c8d;
                border: 2px solid #bdc3c7;
            }
        """)

        # تسمية وحقل تاريخ النهاية
        end_label = QLabel("📅 إلى:")
        end_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                font-weight: bold;
                color: #e74c3c;
                padding: 5px;
            }
        """)

        self.sales_end_date = QDateEdit()
        self.sales_end_date.setCalendarPopup(True)
        self.sales_end_date.setDate(QDate.currentDate())
        self.sales_end_date.setEnabled(False)
        self.sales_end_date.setStyleSheet("""
            QDateEdit {
                border: 2px solid #e74c3c;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QDateEdit:hover {
                border: 2px solid #c0392b;
                background-color: #f8f9fa;
            }
            QDateEdit:disabled {
                background-color: #ecf0f1;
                color: #7f8c8d;
                border: 2px solid #bdc3c7;
            }
        """)

        date_layout.addWidget(start_label)
        date_layout.addWidget(self.sales_start_date)
        date_layout.addWidget(end_label)
        date_layout.addWidget(self.sales_end_date)
        date_layout.addStretch()

        filter_layout.addRow("", date_layout)

        # فلتر العميل مع تصميم محسن
        client_label = QLabel("👤 العميل:")
        client_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)

        self.sales_client_combo = QComboBox()
        self.sales_client_combo.addItem("جميع العملاء", None)
        clients = self.session.query(Client).order_by(Client.name).all()
        for client in clients:
            self.sales_client_combo.addItem(client.name, client.id)
        # تطبيق التصميم المتطور الموحد
        self.style_advanced_combobox(self.sales_client_combo, 'emerald')
        filter_layout.addRow(client_label, self.sales_client_combo)

        # فلتر الحالة مع تصميم محسن
        status_label = QLabel("🔄 الحالة:")
        status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)

        self.sales_status_combo = QComboBox()
        self.sales_status_combo.addItem("جميع الحالات", None)
        self.sales_status_combo.addItem("في الانتظار", "pending")
        self.sales_status_combo.addItem("مكتملة", "completed")
        self.sales_status_combo.addItem("ملغية", "cancelled")
        self.sales_status_combo.addItem("مرتجعة", "returned")
        # تطبيق التصميم المتطور الموحد
        self.style_advanced_combobox(self.sales_status_combo, 'warning')
        filter_layout.addRow(status_label, self.sales_status_combo)

        # تطبيق التصميم الموحد على جميع القوائم المنسدلة في التقارير
        self.apply_unified_combobox_styles()

        # أزرار التحكم مطابقة لباقي البرنامج
        buttons_layout = QHBoxLayout()

        self.sales_apply_button = QPushButton("📊 تطبيق التقرير")
        self.style_advanced_button(self.sales_apply_button, 'emerald')
        self.sales_apply_button.clicked.connect(self.generate_sales_report)

        self.sales_export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.sales_export_button, 'info')

        # إنشاء قائمة منسدلة للتصدير
        sales_export_menu = QMenu(self)
        sales_export_menu.setStyleSheet("QMenu { background-color: white; border: 1px solid #E2E8F0; }")

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(lambda: self.export_to_csv(self.sales_table, "تقرير_المبيعات"))
        sales_export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(lambda: self.print_table(self.sales_table, "تقرير المبيعات"))
        sales_export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(lambda: self.export_to_csv(self.sales_table, "تقرير_المبيعات"))
        sales_export_menu.addAction(csv_action)

        self.sales_export_button.setMenu(sales_export_menu)

        self.sales_refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.sales_refresh_button, 'modern_teal')
        self.sales_refresh_button.clicked.connect(self.generate_sales_report)

        buttons_layout.addWidget(self.sales_apply_button)
        buttons_layout.addWidget(self.sales_export_button)
        buttons_layout.addWidget(self.sales_refresh_button)
        buttons_layout.addStretch()

        filter_layout.addRow("", buttons_layout)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير مع تصميم محسن
        results_group = QGroupBox("📊 نتائج التقرير")
        results_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 3px solid #27ae60;
                border-radius: 15px;
                margin-top: 10px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e8f5e8,
                    stop:1 #c8e6c9);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 5px 15px;
                background-color: #27ae60;
                color: white;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        results_layout = QVBoxLayout()
        results_layout.setContentsMargins(20, 25, 20, 15)
        results_layout.setSpacing(15)

        # ملخص المبيعات مع تصميم محسن
        summary_layout = QHBoxLayout()
        summary_layout.setSpacing(20)

        # عدد الفواتير
        self.sales_count_label = QLabel("📋 عدد الفواتير: 0")
        self.sales_count_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3498db,
                    stop:1 #2980b9);
                color: white;
                border-radius: 12px;
                padding: 15px 20px;
                font-size: 14px;
                font-weight: bold;
                border: 2px solid #2980b9;
            }
        """)

        # إجمالي المبيعات
        self.sales_total_label = QLabel("💰 إجمالي المبيعات: 0.00")
        self.sales_total_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #27ae60,
                    stop:1 #229954);
                color: white;
                border-radius: 12px;
                padding: 15px 20px;
                font-size: 14px;
                font-weight: bold;
                border: 2px solid #229954;
            }
        """)

        # إجمالي الأرباح
        self.sales_profit_label = QLabel("📈 إجمالي الأرباح: 0.00")
        self.sales_profit_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f39c12,
                    stop:1 #e67e22);
                color: white;
                border-radius: 12px;
                padding: 15px 20px;
                font-size: 14px;
                font-weight: bold;
                border: 2px solid #e67e22;
            }
        """)

        summary_layout.addWidget(self.sales_count_label)
        summary_layout.addWidget(self.sales_total_label)
        summary_layout.addWidget(self.sales_profit_label)
        summary_layout.addStretch()

        # جدول المبيعات مطابق لباقي البرنامج
        self.sales_table = QTableWidget()
        self.sales_table.setColumnCount(7)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "🧾 رقم الفاتورة",
            "👨‍💼 العميل",
            "📅 التاريخ",
            "💰 المبلغ الإجمالي",
            "💵 المبلغ المدفوع",
            "💳 طريقة الدفع",
            "🎯 الحالة"
        ]
        self.sales_table.setHorizontalHeaderLabels(headers)

        # تطبيق التصميم المتطور والجميل للجدول
        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للفواتير والمصروفات والإيرادات
        self.sales_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للفواتير والمصروفات والإيرادات
        header = self.sales_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للفواتير والمصروفات والإيرادات
        self.sales_table.verticalHeader().setDefaultSectionSize(45)
        self.sales_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        self.sales_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.sales_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.sales_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.sales_table.setAlternatingRowColors(True)

        results_layout.addLayout(summary_layout)
        results_layout.addWidget(self.sales_table)

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        self.sales_tab.setLayout(layout)

    def setup_purchases_tab(self):
        """إعداد تبويب تقرير المشتريات"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير مع تصميم محسن
        filter_group = QGroupBox("🛒 فلاتر تقرير المشتريات")
        filter_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 3px solid #e74c3c;
                border-radius: 15px;
                margin-top: 10px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #fdf2f2,
                    stop:1 #fadbd8);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 5px 15px;
                background-color: #e74c3c;
                color: white;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        filter_layout = QFormLayout()
        filter_layout.setSpacing(12)
        filter_layout.setContentsMargins(20, 25, 20, 15)

        # فلتر الفترة الزمنية مع تصميم محسن
        period_label = QLabel("📅 الفترة الزمنية:")
        period_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)

        self.purchases_period_combo = QComboBox()
        self.purchases_period_combo.addItems(["اليوم", "الأسبوع الحالي", "الشهر الحالي", "الربع الحالي", "السنة الحالية", "فترة محددة"])
        self.purchases_period_combo.currentIndexChanged.connect(self.on_purchases_period_changed)
        # سيتم تطبيق التصميم الموحد لاحقاً
        filter_layout.addRow(period_label, self.purchases_period_combo)

        # حقول تاريخ البداية والنهاية مع تصميم محسن
        date_layout = QHBoxLayout()
        date_layout.setSpacing(15)

        # تسمية وحقل تاريخ البداية
        start_label = QLabel("📅 من:")
        start_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                font-weight: bold;
                color: #27ae60;
                padding: 5px;
            }
        """)

        self.purchases_start_date = QDateEdit()
        self.purchases_start_date.setCalendarPopup(True)
        self.purchases_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.purchases_start_date.setEnabled(False)
        self.purchases_start_date.setStyleSheet("""
            QDateEdit {
                border: 2px solid #27ae60;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QDateEdit:hover {
                border: 2px solid #229954;
                background-color: #f8f9fa;
            }
            QDateEdit:disabled {
                background-color: #ecf0f1;
                color: #7f8c8d;
                border: 2px solid #bdc3c7;
            }
        """)

        # تسمية وحقل تاريخ النهاية
        end_label = QLabel("📅 إلى:")
        end_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                font-weight: bold;
                color: #e74c3c;
                padding: 5px;
            }
        """)

        self.purchases_end_date = QDateEdit()
        self.purchases_end_date.setCalendarPopup(True)
        self.purchases_end_date.setDate(QDate.currentDate())
        self.purchases_end_date.setEnabled(False)
        self.purchases_end_date.setStyleSheet("""
            QDateEdit {
                border: 2px solid #e74c3c;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QDateEdit:hover {
                border: 2px solid #c0392b;
                background-color: #f8f9fa;
            }
            QDateEdit:disabled {
                background-color: #ecf0f1;
                color: #7f8c8d;
                border: 2px solid #bdc3c7;
            }
        """)

        date_layout.addWidget(start_label)
        date_layout.addWidget(self.purchases_start_date)
        date_layout.addWidget(end_label)
        date_layout.addWidget(self.purchases_end_date)
        date_layout.addStretch()

        filter_layout.addRow("", date_layout)

        # فلتر المورد مع تصميم محسن
        supplier_label = QLabel("🏭 المورد:")
        supplier_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)

        self.purchases_supplier_combo = QComboBox()
        self.purchases_supplier_combo.addItem("جميع الموردين", None)
        suppliers = self.session.query(Supplier).order_by(Supplier.name).all()
        for supplier in suppliers:
            self.purchases_supplier_combo.addItem(supplier.name, supplier.id)
        self.purchases_supplier_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #8e44ad;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QComboBox:hover {
                border: 2px solid #7d3c98;
                background-color: #f8f9fa;
            }
            QComboBox:focus {
                border: 2px solid #1abc9c;
                background-color: #ffffff;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 5px solid transparent;
                border-top: 8px solid #8e44ad;
                margin-right: 5px;
            }
        """)
        filter_layout.addRow(supplier_label, self.purchases_supplier_combo)

        # فلتر الحالة مع تصميم محسن
        status_label = QLabel("🔄 الحالة:")
        status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)

        self.purchases_status_combo = QComboBox()
        self.purchases_status_combo.addItem("جميع الحالات", None)
        self.purchases_status_combo.addItem("في الانتظار", "pending")
        self.purchases_status_combo.addItem("مستلم جزئياً", "partially_received")
        self.purchases_status_combo.addItem("مستلم بالكامل", "completed")
        self.purchases_status_combo.addItem("ملغي", "cancelled")
        self.purchases_status_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #d35400;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QComboBox:hover {
                border: 2px solid #ba4a00;
                background-color: #f8f9fa;
            }
            QComboBox:focus {
                border: 2px solid #1abc9c;
                background-color: #ffffff;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 5px solid transparent;
                border-top: 8px solid #d35400;
                margin-right: 5px;
            }
        """)
        filter_layout.addRow(status_label, self.purchases_status_combo)

        # أزرار التحكم مطابقة لباقي البرنامج
        buttons_layout = QHBoxLayout()

        self.purchases_apply_button = QPushButton("🛒 تطبيق التقرير")
        self.style_advanced_button(self.purchases_apply_button, 'primary')
        self.purchases_apply_button.clicked.connect(self.generate_purchases_report)

        self.purchases_export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.purchases_export_button, 'info')

        # إنشاء قائمة منسدلة للتصدير
        purchases_export_menu = QMenu(self)
        purchases_export_menu.setStyleSheet("QMenu { background-color: white; border: 1px solid #E2E8F0; }")

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(lambda: self.export_to_csv(self.purchases_table, "تقرير_المشتريات"))
        purchases_export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(lambda: self.print_table(self.purchases_table, "تقرير المشتريات"))
        purchases_export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(lambda: self.export_to_csv(self.purchases_table, "تقرير_المشتريات"))
        purchases_export_menu.addAction(csv_action)

        self.purchases_export_button.setMenu(purchases_export_menu)

        self.purchases_refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.purchases_refresh_button, 'modern_teal')
        self.purchases_refresh_button.clicked.connect(self.generate_purchases_report)

        buttons_layout.addWidget(self.purchases_apply_button)
        buttons_layout.addWidget(self.purchases_export_button)
        buttons_layout.addWidget(self.purchases_refresh_button)
        buttons_layout.addStretch()

        filter_layout.addRow("", buttons_layout)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير مع تصميم محسن
        results_group = QGroupBox("🛒 نتائج تقرير المشتريات")
        results_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 3px solid #e74c3c;
                border-radius: 15px;
                margin-top: 10px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #fdf2f2,
                    stop:1 #fadbd8);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 5px 15px;
                background-color: #e74c3c;
                color: white;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        results_layout = QVBoxLayout()
        results_layout.setContentsMargins(20, 25, 20, 15)
        results_layout.setSpacing(15)

        # ملخص المشتريات مع تصميم محسن
        summary_layout = QHBoxLayout()
        summary_layout.setSpacing(20)

        # عدد أوامر الشراء
        self.purchases_count_label = QLabel("🛒 عدد أوامر الشراء: 0")
        self.purchases_count_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #e74c3c,
                    stop:1 #c0392b);
                color: white;
                border-radius: 12px;
                padding: 15px 20px;
                font-size: 14px;
                font-weight: bold;
                border: 2px solid #c0392b;
            }
        """)

        # إجمالي المشتريات
        self.purchases_total_label = QLabel("💰 إجمالي المشتريات: 0.00")
        self.purchases_total_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #9b59b6,
                    stop:1 #8e44ad);
                color: white;
                border-radius: 12px;
                padding: 15px 20px;
                font-size: 14px;
                font-weight: bold;
                border: 2px solid #8e44ad;
            }
        """)

        # إجمالي المدفوعات
        self.purchases_paid_label = QLabel("💳 إجمالي المدفوعات: 0.00")
        self.purchases_paid_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f39c12,
                    stop:1 #e67e22);
                color: white;
                border-radius: 12px;
                padding: 15px 20px;
                font-size: 14px;
                font-weight: bold;
                border: 2px solid #e67e22;
            }
        """)

        summary_layout.addWidget(self.purchases_count_label)
        summary_layout.addWidget(self.purchases_total_label)
        summary_layout.addWidget(self.purchases_paid_label)
        summary_layout.addStretch()

        # جدول المشتريات مطابق لباقي البرنامج
        self.purchases_table = QTableWidget()
        self.purchases_table.setColumnCount(6)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "🧾 رقم أمر الشراء",
            "🏢 المورد",
            "📅 التاريخ",
            "💰 المبلغ الإجمالي",
            "💵 المبلغ المدفوع",
            "🎯 الحالة"
        ]
        self.purchases_table.setHorizontalHeaderLabels(headers)

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للفواتير والمصروفات والإيرادات
        self.purchases_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للفواتير والمصروفات والإيرادات
        header = self.purchases_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للفواتير والمصروفات والإيرادات
        self.purchases_table.verticalHeader().setDefaultSectionSize(45)
        self.purchases_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        self.purchases_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.purchases_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.purchases_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.purchases_table.setAlternatingRowColors(True)

        results_layout.addLayout(summary_layout)
        results_layout.addWidget(self.purchases_table)

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        self.purchases_tab.setLayout(layout)

    def setup_profit_loss_tab(self):
        """إعداد تبويب تقرير الأرباح والخسائر"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير مع تصميم محسن
        filter_group = QGroupBox("💰 فلاتر تقرير الأرباح والخسائر")
        filter_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 3px solid #f39c12;
                border-radius: 15px;
                margin-top: 10px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #fef9e7,
                    stop:0.7 #fcf3cf,
                    stop:1 #f7dc6f);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 5px 15px;
                background-color: #f39c12;
                color: white;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        filter_layout = QFormLayout()
        filter_layout.setSpacing(12)
        filter_layout.setContentsMargins(20, 25, 20, 15)

        # فلتر الفترة الزمنية مع تصميم محسن
        period_label = QLabel("📅 الفترة الزمنية:")
        period_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)

        self.profit_loss_period_combo = QComboBox()
        self.profit_loss_period_combo.addItems(["اليوم", "الأسبوع الحالي", "الشهر الحالي", "الربع الحالي", "السنة الحالية", "فترة محددة"])
        self.profit_loss_period_combo.currentIndexChanged.connect(self.on_profit_loss_period_changed)
        self.profit_loss_period_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #f39c12;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QComboBox:hover {
                border: 2px solid #e67e22;
                background-color: #f8f9fa;
            }
            QComboBox:focus {
                border: 2px solid #1abc9c;
                background-color: #ffffff;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 5px solid transparent;
                border-top: 8px solid #f39c12;
                margin-right: 5px;
            }
        """)
        filter_layout.addRow(period_label, self.profit_loss_period_combo)

        # حقول تاريخ البداية والنهاية مع تصميم محسن
        date_layout = QHBoxLayout()
        date_layout.setSpacing(15)

        # تسمية وحقل تاريخ البداية
        start_label = QLabel("📅 من:")
        start_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                font-weight: bold;
                color: #27ae60;
                padding: 5px;
            }
        """)

        self.profit_loss_start_date = QDateEdit()
        self.profit_loss_start_date.setCalendarPopup(True)
        self.profit_loss_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.profit_loss_start_date.setEnabled(False)
        self.profit_loss_start_date.setStyleSheet("""
            QDateEdit {
                border: 2px solid #27ae60;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QDateEdit:hover {
                border: 2px solid #229954;
                background-color: #f8f9fa;
            }
            QDateEdit:disabled {
                background-color: #ecf0f1;
                color: #7f8c8d;
                border: 2px solid #bdc3c7;
            }
        """)

        # تسمية وحقل تاريخ النهاية
        end_label = QLabel("📅 إلى:")
        end_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                font-weight: bold;
                color: #e74c3c;
                padding: 5px;
            }
        """)

        self.profit_loss_end_date = QDateEdit()
        self.profit_loss_end_date.setCalendarPopup(True)
        self.profit_loss_end_date.setDate(QDate.currentDate())
        self.profit_loss_end_date.setEnabled(False)
        self.profit_loss_end_date.setStyleSheet("""
            QDateEdit {
                border: 2px solid #e74c3c;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QDateEdit:hover {
                border: 2px solid #c0392b;
                background-color: #f8f9fa;
            }
            QDateEdit:disabled {
                background-color: #ecf0f1;
                color: #7f8c8d;
                border: 2px solid #bdc3c7;
            }
        """)

        date_layout.addWidget(start_label)
        date_layout.addWidget(self.profit_loss_start_date)
        date_layout.addWidget(end_label)
        date_layout.addWidget(self.profit_loss_end_date)
        date_layout.addStretch()

        filter_layout.addRow("", date_layout)

        # أزرار التحكم مطابقة لباقي البرنامج
        buttons_layout = QHBoxLayout()

        self.profit_loss_apply_button = QPushButton("💰 تطبيق التقرير")
        self.style_advanced_button(self.profit_loss_apply_button, 'warning')
        self.profit_loss_apply_button.clicked.connect(self.generate_profit_loss_report)

        self.profit_loss_export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.profit_loss_export_button, 'info')

        # إنشاء قائمة منسدلة للتصدير
        profit_loss_export_menu = QMenu(self)
        profit_loss_export_menu.setStyleSheet("QMenu { background-color: white; border: 1px solid #E2E8F0; }")

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(lambda: self.export_profit_loss_to_pdf())
        profit_loss_export_menu.addAction(pdf_action)

        html_action = QAction("🌐 حفظ كـ HTML", self)
        html_action.triggered.connect(lambda: self.save_profit_loss_html())
        profit_loss_export_menu.addAction(html_action)

        self.profit_loss_export_button.setMenu(profit_loss_export_menu)

        self.profit_loss_refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.profit_loss_refresh_button, 'modern_teal')
        self.profit_loss_refresh_button.clicked.connect(self.generate_profit_loss_report)

        buttons_layout.addWidget(self.profit_loss_apply_button)
        buttons_layout.addWidget(self.profit_loss_export_button)
        buttons_layout.addWidget(self.profit_loss_refresh_button)
        buttons_layout.addStretch()

        filter_layout.addRow("", buttons_layout)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير
        results_group = QGroupBox("نتائج التقرير")
        results_group
        results_layout = QVBoxLayout()

        # تقرير الأرباح والخسائر مع تصميم محسن
        self.profit_loss_summary = QTextBrowser()
        self.profit_loss_summary.setMinimumHeight(500)
        self.profit_loss_summary.setStyleSheet("""
            QTextBrowser {
                border: 3px solid #f39c12;
                border-radius: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #fef9e7,
                    stop:0.7 #fcf3cf,
                    stop:1 #f7dc6f);
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                font-size: 14px;
                color: #2c3e50;
                padding: 20px;
                line-height: 1.6;
            }
            QScrollBar:vertical {
                border: 2px solid #f39c12;
                background: #fef9e7;
                width: 15px;
                border-radius: 7px;
            }
            QScrollBar::handle:vertical {
                background: #f39c12;
                border-radius: 7px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #e67e22;
            }
        """)

        results_layout.addWidget(self.profit_loss_summary)

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        self.profit_loss_tab.setLayout(layout)

    def setup_inventory_tab(self):
        """إعداد تبويب تقرير المخزون"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير مع تصميم محسن
        filter_group = QGroupBox("📦 فلاتر تقرير المخزون")
        filter_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 3px solid #3498db;
                border-radius: 15px;
                margin-top: 10px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e3f2fd,
                    stop:1 #bbdefb);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 5px 15px;
                background-color: #3498db;
                color: white;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        filter_layout = QFormLayout()
        filter_layout.setSpacing(12)
        filter_layout.setContentsMargins(20, 25, 20, 15)

        # فلتر الفئة مع تصميم محسن
        category_label = QLabel("📂 الفئة:")
        category_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)

        self.inventory_category_combo = QComboBox()
        self.inventory_category_combo.addItem("جميع الفئات", None)
        categories = self.session.query(Inventory.category).distinct().all()
        for category in categories:
            if category[0]:
                self.inventory_category_combo.addItem(category[0], category[0])
        self.inventory_category_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #3498db;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QComboBox:hover {
                border: 2px solid #2980b9;
                background-color: #f8f9fa;
            }
            QComboBox:focus {
                border: 2px solid #1abc9c;
                background-color: #ffffff;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 5px solid transparent;
                border-top: 8px solid #3498db;
                margin-right: 5px;
            }
        """)
        filter_layout.addRow(category_label, self.inventory_category_combo)

        # فلتر المورد مع تصميم محسن
        supplier_label = QLabel("🏭 المورد:")
        supplier_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)

        self.inventory_supplier_combo = QComboBox()
        self.inventory_supplier_combo.addItem("جميع الموردين", None)
        suppliers = self.session.query(Supplier).order_by(Supplier.name).all()
        for supplier in suppliers:
            self.inventory_supplier_combo.addItem(supplier.name, supplier.id)
        self.inventory_supplier_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #9b59b6;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QComboBox:hover {
                border: 2px solid #8e44ad;
                background-color: #f8f9fa;
            }
            QComboBox:focus {
                border: 2px solid #1abc9c;
                background-color: #ffffff;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 5px solid transparent;
                border-top: 8px solid #9b59b6;
                margin-right: 5px;
            }
        """)
        filter_layout.addRow(supplier_label, self.inventory_supplier_combo)

        # فلتر حالة المخزون مع تصميم محسن
        status_label = QLabel("📊 حالة المخزون:")
        status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)

        self.inventory_status_combo = QComboBox()
        self.inventory_status_combo.addItem("جميع العناصر", "all")
        self.inventory_status_combo.addItem("عناصر منخفضة المخزون", "low_stock")
        self.inventory_status_combo.addItem("عناصر نفدت من المخزون", "out_of_stock")
        self.inventory_status_combo.addItem("عناصر متوفرة", "in_stock")
        self.inventory_status_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #e74c3c;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QComboBox:hover {
                border: 2px solid #c0392b;
                background-color: #f8f9fa;
            }
            QComboBox:focus {
                border: 2px solid #1abc9c;
                background-color: #ffffff;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 5px solid transparent;
                border-top: 8px solid #e74c3c;
                margin-right: 5px;
            }
        """)
        filter_layout.addRow(status_label, self.inventory_status_combo)

        # أزرار التحكم مطابقة لباقي البرنامج
        buttons_layout = QHBoxLayout()

        self.inventory_apply_button = QPushButton("📦 تطبيق التقرير")
        self.style_advanced_button(self.inventory_apply_button, 'info')
        self.inventory_apply_button.clicked.connect(self.generate_inventory_report)

        self.inventory_export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.inventory_export_button, 'info')

        # إنشاء قائمة منسدلة للتصدير
        inventory_export_menu = QMenu(self)
        inventory_export_menu.setStyleSheet("QMenu { background-color: white; border: 1px solid #E2E8F0; }")

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(lambda: self.export_to_csv(self.inventory_table, "تقرير_المخزون"))
        inventory_export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(lambda: self.print_table(self.inventory_table, "تقرير المخزون"))
        inventory_export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(lambda: self.export_to_csv(self.inventory_table, "تقرير_المخزون"))
        inventory_export_menu.addAction(csv_action)

        self.inventory_export_button.setMenu(inventory_export_menu)

        self.inventory_refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.inventory_refresh_button, 'modern_teal')
        self.inventory_refresh_button.clicked.connect(self.generate_inventory_report)

        buttons_layout.addWidget(self.inventory_apply_button)
        buttons_layout.addWidget(self.inventory_export_button)
        buttons_layout.addWidget(self.inventory_refresh_button)
        buttons_layout.addStretch()

        filter_layout.addRow("", buttons_layout)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير مع تصميم محسن
        results_group = QGroupBox("📦 نتائج تقرير المخزون")
        results_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 3px solid #3498db;
                border-radius: 15px;
                margin-top: 10px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e3f2fd,
                    stop:1 #bbdefb);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 5px 15px;
                background-color: #3498db;
                color: white;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        results_layout = QVBoxLayout()
        results_layout.setContentsMargins(20, 25, 20, 15)
        results_layout.setSpacing(15)

        # ملخص المخزون مع تصميم محسن
        summary_layout = QHBoxLayout()
        summary_layout.setSpacing(20)

        # عدد العناصر
        self.inventory_count_label = QLabel("📦 عدد العناصر: 0")
        self.inventory_count_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3498db,
                    stop:1 #2980b9);
                color: white;
                border-radius: 12px;
                padding: 15px 20px;
                font-size: 14px;
                font-weight: bold;
                border: 2px solid #2980b9;
            }
        """)

        # قيمة المخزون
        self.inventory_value_label = QLabel("💰 قيمة المخزون: 0.00")
        self.inventory_value_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #27ae60,
                    stop:1 #229954);
                color: white;
                border-radius: 12px;
                padding: 15px 20px;
                font-size: 14px;
                font-weight: bold;
                border: 2px solid #229954;
            }
        """)

        # عناصر منخفضة المخزون
        self.inventory_low_stock_label = QLabel("⚠️ عناصر منخفضة المخزون: 0")
        self.inventory_low_stock_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #e74c3c,
                    stop:1 #c0392b);
                color: white;
                border-radius: 12px;
                padding: 15px 20px;
                font-size: 14px;
                font-weight: bold;
                border: 2px solid #c0392b;
            }
        """)

        summary_layout.addWidget(self.inventory_count_label)
        summary_layout.addWidget(self.inventory_value_label)
        summary_layout.addWidget(self.inventory_low_stock_label)
        summary_layout.addStretch()

        # جدول المخزون مطابق لباقي البرنامج
        self.inventory_table = QTableWidget()
        self.inventory_table.setColumnCount(8)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "📦 الاسم",
            "🏷️ الفئة",
            "📏 الوحدة",
            "🔢 الكمية",
            "⚠️ الحد الأدنى",
            "💸 سعر التكلفة",
            "💰 سعر البيع",
            "🏢 المورد"
        ]
        self.inventory_table.setHorizontalHeaderLabels(headers)

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للفواتير والمصروفات والإيرادات
        self.inventory_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للفواتير والمصروفات والإيرادات
        header = self.inventory_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للفواتير والمصروفات والإيرادات
        self.inventory_table.verticalHeader().setDefaultSectionSize(45)
        self.inventory_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        self.inventory_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.inventory_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.inventory_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.inventory_table.setAlternatingRowColors(True)

        results_layout.addLayout(summary_layout)
        results_layout.addWidget(self.inventory_table)

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        self.inventory_tab.setLayout(layout)

    def setup_clients_tab(self):
        """إعداد تبويب تقرير العملاء"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير مع تصميم محسن
        filter_group = QGroupBox("🤝 فلاتر تقرير العملاء")
        filter_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                border: 4px solid #000000;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 18px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 25px;
                padding: 8px 20px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 3px solid #000000;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
        """)
        filter_layout = QFormLayout()
        filter_layout.setSpacing(12)
        filter_layout.setContentsMargins(20, 25, 20, 15)

        # فلتر العميل مع تصميم محسن
        client_label = QLabel("👤 العميل:")
        client_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #475569, stop:0.5 #2563EB, stop:1 #475569);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
            }
        """)

        self.clients_client_combo = QComboBox()
        self.clients_client_combo.addItem("جميع العملاء", None)
        clients = self.session.query(Client).order_by(Client.name).all()
        for client in clients:
            self.clients_client_combo.addItem(client.name, client.id)
        self.clients_client_combo.setStyleSheet("""
            QComboBox {
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 10px 15px;
                font-size: 15px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f8fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                color: #1e293b;
                min-height: 30px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }
            QComboBox:hover {
                border: 3px solid #2563EB;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #dbeafe, stop:0.5 #bfdbfe, stop:1 #93c5fd);
                color: #1e40af;
            }
            QComboBox:focus {
                border: 3px solid #1d4ed8;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ffffff, stop:0.5 #dbeafe, stop:1 #bfdbfe);
                color: #1e40af;
            }
            QComboBox::drop-down {
                border: none;
                width: 35px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2563EB, stop:1 #1d4ed8);
                border-radius: 5px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 5px solid transparent;
                border-top: 8px solid #e91e63;
                margin-right: 5px;
            }
        """)
        filter_layout.addRow(client_label, self.clients_client_combo)

        # أزرار التحكم مطابقة لباقي البرنامج
        buttons_layout = QHBoxLayout()

        self.clients_apply_button = QPushButton("🤝 تطبيق التقرير")
        self.style_advanced_button(self.clients_apply_button, 'emerald')
        self.clients_apply_button.clicked.connect(self.generate_clients_report)

        self.clients_export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.clients_export_button, 'info')

        # إنشاء قائمة منسدلة للتصدير
        clients_export_menu = QMenu(self)
        clients_export_menu.setStyleSheet("QMenu { background-color: white; border: 1px solid #E2E8F0; }")

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(lambda: self.export_to_csv(self.clients_sales_table, "تقرير_العملاء"))
        clients_export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(lambda: self.print_table(self.clients_sales_table, "تقرير العملاء"))
        clients_export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(lambda: self.export_to_csv(self.clients_sales_table, "تقرير_العملاء"))
        clients_export_menu.addAction(csv_action)

        self.clients_export_button.setMenu(clients_export_menu)

        self.clients_refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.clients_refresh_button, 'modern_teal')
        self.clients_refresh_button.clicked.connect(self.generate_clients_report)

        buttons_layout.addWidget(self.clients_apply_button)
        buttons_layout.addWidget(self.clients_export_button)
        buttons_layout.addWidget(self.clients_refresh_button)
        buttons_layout.addStretch()

        filter_layout.addRow("", buttons_layout)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير مع تصميم محسن
        results_group = QGroupBox("🤝 نتائج تقرير العملاء")
        results_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                border: 4px solid #000000;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 18px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 25px;
                padding: 8px 20px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 3px solid #000000;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
        """)
        results_layout = QVBoxLayout()
        results_layout.setContentsMargins(20, 25, 20, 15)
        results_layout.setSpacing(15)

        # تقرير العملاء مع تصميم محسن
        self.clients_summary = QTextBrowser()
        self.clients_summary.setMinimumHeight(300)
        self.clients_summary.setStyleSheet("""
            QTextBrowser {
                border: 4px solid #000000;
                border-radius: 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(248, 250, 252, 0.95),
                    stop:0.3 rgba(241, 245, 249, 0.9),
                    stop:0.7 rgba(226, 232, 240, 0.85),
                    stop:1 rgba(255, 255, 255, 0.9));
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: #1e293b;
                padding: 25px;
                line-height: 1.8;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }
            QScrollBar:vertical {
                border: 3px solid #000000;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e2e8f0, stop:1 #cbd5e1);
                width: 18px;
                border-radius: 9px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2563EB, stop:1 #1d4ed8);
                border: 2px solid #000000;
                border-radius: 7px;
                min-height: 25px;
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3B82F6, stop:1 #2563EB);
            }
        """)

        # جدول مبيعات العميل مطابق لباقي البرنامج
        self.clients_sales_table = QTableWidget()
        self.clients_sales_table.setColumnCount(6)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "🧾 رقم الفاتورة",
            "📅 التاريخ",
            "💰 المبلغ الإجمالي",
            "💵 المبلغ المدفوع",
            "💳 طريقة الدفع",
            "🎯 الحالة"
        ]
        self.clients_sales_table.setHorizontalHeaderLabels(headers)

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للتصميم الموحد
        self.clients_sales_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(0, 0, 0, 0.3);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                border: 4px solid #000000;
                border-radius: 15px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 15px;
                font-weight: bold;
                selection-background-color: rgba(37, 99, 235, 0.2);
                alternate-background-color: rgba(30, 41, 59, 0.1);
                outline: none;
                padding: 8px;
                color: #1e293b;
            }

            QTableWidget::item {
                padding: 12px 15px;
                border: 2px solid rgba(0, 0, 0, 0.2);
                text-align: center;
                min-height: 35px;
                max-height: 50px;
                font-weight: bold;
                font-size: 15px;
                border-radius: 8px;
                margin: 2px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(248, 250, 252, 0.95),
                    stop:0.5 rgba(226, 232, 240, 0.9),
                    stop:1 rgba(203, 213, 225, 0.85));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b) !important;
                color: #ffffff !important;
                border: 3px solid #2563EB !important;
                border-radius: 10px !important;
                font-weight: bold !important;
                font-size: 16px !important;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6) !important;
                box-shadow: 0 3px 8px rgba(37, 99, 235, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569) !important;
                border: 3px solid #3B82F6 !important;
                border-radius: 10px !important;
                color: #ffffff !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
                box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2) !important;
            }

            QHeaderView::section {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                padding: 15px 20px;
                margin: 0px;
                font-weight: bold;
                font-size: 16px;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                border: 3px solid #000000;
                border-bottom: 4px solid #000000;
                text-align: center;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569);
                color: #ffffff;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للفواتير والمصروفات والإيرادات
        header = self.clients_sales_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
                background-clip: padding-box !important;
                box-shadow: inset 0 2px 4px rgba(255, 255, 255, 0.2),
                           0 4px 8px rgba(0, 0, 0, 0.3) !important;
            }

            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #2563EB,
                    stop:0.3 #3B82F6, stop:0.4 #6366F1, stop:0.5 #8B5CF6,
                    stop:0.6 #A855F7, stop:0.7 #C084FC, stop:0.8 #E879F9,
                    stop:0.9 #F0ABFC, stop:1 #FBBF24) !important;
                color: #FFFFFF !important;
                text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.9) !important;
                transform: translateY(-2px) !important;
                box-shadow: inset 0 2px 6px rgba(255, 255, 255, 0.3),
                           0 6px 12px rgba(0, 0, 0, 0.4) !important;
            }

            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E293B, stop:0.4 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.8 #6366F1, stop:1 #7C3AED) !important;
                color: #FFFFFF !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9) !important;
                transform: translateY(1px) !important;
                box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.4) !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للفواتير والمصروفات والإيرادات
        self.clients_sales_table.verticalHeader().setDefaultSectionSize(45)
        self.clients_sales_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        self.clients_sales_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.clients_sales_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.clients_sales_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.clients_sales_table.setAlternatingRowColors(True)

        # إضافة ملخص إحصائي متطور
        stats_layout = QHBoxLayout()

        # عدد العملاء
        self.clients_count_label = StyledLabel("عدد العملاء: 0", "info")
        stats_layout.addWidget(self.clients_count_label.label)

        # إجمالي المبيعات
        self.clients_total_sales_label = StyledLabel("إجمالي المبيعات: 0.00 ج.م", "success")
        stats_layout.addWidget(self.clients_total_sales_label.label)

        # متوسط المبيعات
        self.clients_avg_sales_label = StyledLabel("متوسط المبيعات: 0.00 ج.م", "warning")
        stats_layout.addWidget(self.clients_avg_sales_label.label)

        results_layout.addLayout(stats_layout)
        results_layout.addWidget(self.clients_summary)

        # عنوان الجدول مع تصميم محسن مطابق للتصميم الموحد
        table_title = QLabel("📊 تفاصيل مبيعات العملاء")
        table_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                padding: 15px 25px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                border: 4px solid #000000;
                border-radius: 12px;
                margin: 8px 0;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
        """)
        results_layout.addWidget(table_title)
        results_layout.addWidget(self.clients_sales_table)

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        self.clients_tab.setLayout(layout)

    # دوال معالجة الأحداث
    def on_sales_period_changed(self, index):
        """معالجة تغيير الفترة الزمنية في تقرير المبيعات"""
        try:
            period = self.sales_period_combo.currentText()
            if period == "فترة محددة":
                self.sales_start_date.setEnabled(True)
                self.sales_end_date.setEnabled(True)
            else:
                self.sales_start_date.setEnabled(False)
                self.sales_end_date.setEnabled(False)
                self.set_date_range(period, self.sales_start_date, self.sales_end_date)
        except Exception as e:
            print(f"خطأ في معالجة تغيير الفترة الزمنية في تقرير المبيعات: {str(e)}")

    def on_purchases_period_changed(self, index):
        """معالجة تغيير الفترة الزمنية في تقرير المشتريات"""
        try:
            period = self.purchases_period_combo.currentText()
            if period == "فترة محددة":
                self.purchases_start_date.setEnabled(True)
                self.purchases_end_date.setEnabled(True)
            else:
                self.purchases_start_date.setEnabled(False)
                self.purchases_end_date.setEnabled(False)
                self.set_date_range(period, self.purchases_start_date, self.purchases_end_date)
        except Exception as e:
            print(f"خطأ في معالجة تغيير الفترة الزمنية في تقرير المشتريات: {str(e)}")

    def on_profit_loss_period_changed(self, index):
        """معالجة تغيير الفترة الزمنية في تقرير الأرباح والخسائر"""
        try:
            period = self.profit_loss_period_combo.currentText()
            if period == "فترة محددة":
                self.profit_loss_start_date.setEnabled(True)
                self.profit_loss_end_date.setEnabled(True)
            else:
                self.profit_loss_start_date.setEnabled(False)
                self.profit_loss_end_date.setEnabled(False)
                self.set_date_range(period, self.profit_loss_start_date, self.profit_loss_end_date)
        except Exception as e:
            print(f"خطأ في معالجة تغيير الفترة الزمنية في تقرير الأرباح والخسائر: {str(e)}")

    def on_expenses_period_changed(self, index):
        """معالجة تغيير الفترة الزمنية في تقرير المصروفات"""
        try:
            period = self.expenses_period_combo.currentText()
            if period == "فترة محددة":
                self.expenses_start_date.setEnabled(True)
                self.expenses_end_date.setEnabled(True)
            else:
                self.expenses_start_date.setEnabled(False)
                self.expenses_end_date.setEnabled(False)
                self.set_date_range(period, self.expenses_start_date, self.expenses_end_date)
        except Exception as e:
            print(f"خطأ في معالجة تغيير الفترة الزمنية في تقرير المصروفات: {str(e)}")

    def on_revenues_period_changed(self, index):
        """معالجة تغيير الفترة الزمنية في تقرير الإيرادات"""
        try:
            period = self.revenues_period_combo.currentText()
            if period == "فترة محددة":
                self.revenues_start_date.setEnabled(True)
                self.revenues_end_date.setEnabled(True)
            else:
                self.revenues_start_date.setEnabled(False)
                self.revenues_end_date.setEnabled(False)
                self.set_date_range(period, self.revenues_start_date, self.revenues_end_date)
        except Exception as e:
            print(f"خطأ في معالجة تغيير الفترة الزمنية في تقرير الإيرادات: {str(e)}")

    def set_date_range(self, period, start_date_widget, end_date_widget):
        """تعيين نطاق التاريخ بناءً على الفترة المحددة"""
        today = QDate.currentDate()

        if period == "اليوم":
            start_date_widget.setDate(today)
            end_date_widget.setDate(today)
        elif period == "الأسبوع الحالي":
            start_of_week = today.addDays(-today.dayOfWeek() + 1)
            start_date_widget.setDate(start_of_week)
            end_date_widget.setDate(today)
        elif period == "الشهر الحالي":
            start_of_month = QDate(today.year(), today.month(), 1)
            start_date_widget.setDate(start_of_month)
            end_date_widget.setDate(today)
        elif period == "الربع الحالي":
            quarter = (today.month() - 1) // 3 + 1
            start_month = (quarter - 1) * 3 + 1
            start_of_quarter = QDate(today.year(), start_month, 1)
            start_date_widget.setDate(start_of_quarter)
            end_date_widget.setDate(today)
        elif period == "السنة الحالية":
            start_of_year = QDate(today.year(), 1, 1)
            start_date_widget.setDate(start_of_year)
            end_date_widget.setDate(today)

    # دوال إنتاج التقارير
    def generate_sales_report(self):
        """إنتاج تقرير المبيعات"""
        try:
            # الحصول على الفلاتر
            start_date = qdate_to_datetime(self.sales_start_date.date())
            end_date = qdate_to_datetime(self.sales_end_date.date())
            client_id = self.sales_client_combo.currentData()
            status = self.sales_status_combo.currentData()

            # بناء الاستعلام
            query = self.session.query(Sale).filter(
                Sale.date >= start_date,
                Sale.date <= end_date
            )

            # تطبيق الفلاتر
            if client_id:
                query = query.filter(Sale.client_id == client_id)
            if status:
                query = query.filter(Sale.status == status)

            # تنفيذ الاستعلام
            sales = query.order_by(Sale.date.desc()).all()

            # حساب الإحصائيات
            total_sales = sum(sale.total_amount for sale in sales)
            total_profit = 0

            # حساب الأرباح من عناصر المبيعات
            for sale in sales:
                sale_items = self.session.query(SaleItem).filter(SaleItem.sale_id == sale.id).all()
                for item in sale_items:
                    inventory_item = self.session.query(Inventory).get(item.inventory_id)
                    if inventory_item:
                        profit_per_unit = item.unit_price - inventory_item.cost_price
                        total_profit += profit_per_unit * item.quantity

            # تحديث الملخص
            self.sales_count_label.label.setText(f"عدد الفواتير: {len(sales)}")
            self.sales_total_label.label.setText(f"إجمالي المبيعات: {format_currency(total_sales)}")
            self.sales_profit_label.label.setText(f"إجمالي الأرباح: {format_currency(total_profit)}")

            # تحديث الجدول
            self.sales_table.setRowCount(len(sales))
            for row, sale in enumerate(sales):
                # رقم الفاتورة
                self.sales_table.setItem(row, 0, QTableWidgetItem(sale.sale_number or ""))

                # العميل
                client_name = ""
                if sale.client:
                    client_name = sale.client.name
                self.sales_table.setItem(row, 1, QTableWidgetItem(client_name))

                # التاريخ
                date_str = sale.date.strftime("%Y-%m-%d") if sale.date else ""
                self.sales_table.setItem(row, 2, QTableWidgetItem(date_str))

                # المبلغ الإجمالي
                self.sales_table.setItem(row, 3, QTableWidgetItem(format_currency(sale.total_amount)))

                # المبلغ المدفوع
                self.sales_table.setItem(row, 4, QTableWidgetItem(format_currency(sale.paid_amount)))

                # طريقة الدفع
                payment_methods = {
                    'cash': 'نقدي',
                    'credit': 'آجل',
                    'bank_transfer': 'تحويل بنكي',
                    'check': 'شيك'
                }
                payment_method = payment_methods.get(sale.payment_method, sale.payment_method or "")
                self.sales_table.setItem(row, 5, QTableWidgetItem(payment_method))

                # الحالة
                statuses = {
                    'pending': 'في الانتظار',
                    'completed': 'مكتملة',
                    'cancelled': 'ملغية',
                    'returned': 'مرتجعة'
                }
                status_text = statuses.get(sale.status, sale.status or "")
                self.sales_table.setItem(row, 6, QTableWidgetItem(status_text))

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير المبيعات: {str(e)}")

    def generate_purchases_report(self):
        """إنتاج تقرير المشتريات"""
        try:
            # الحصول على الفلاتر
            start_date = qdate_to_datetime(self.purchases_start_date.date())
            end_date = qdate_to_datetime(self.purchases_end_date.date())
            supplier_id = self.purchases_supplier_combo.currentData()
            status = self.purchases_status_combo.currentData()

            # بناء الاستعلام
            query = self.session.query(Purchase).filter(
                Purchase.date >= start_date,
                Purchase.date <= end_date
            )

            # تطبيق الفلاتر
            if supplier_id:
                query = query.filter(Purchase.supplier_id == supplier_id)
            if status:
                query = query.filter(Purchase.status == status)

            # تنفيذ الاستعلام
            purchases = query.order_by(Purchase.date.desc()).all()

            # حساب الإحصائيات
            total_purchases = sum(purchase.total_amount for purchase in purchases)
            total_paid = sum(purchase.paid_amount for purchase in purchases)

            # تحديث الملخص
            self.purchases_count_label.label.setText(f"عدد أوامر الشراء: {len(purchases)}")
            self.purchases_total_label.label.setText(f"إجمالي المشتريات: {format_currency(total_purchases)}")
            self.purchases_paid_label.label.setText(f"إجمالي المدفوعات: {format_currency(total_paid)}")

            # تحديث الجدول
            self.purchases_table.setRowCount(len(purchases))
            for row, purchase in enumerate(purchases):
                # رقم أمر الشراء
                self.purchases_table.setItem(row, 0, QTableWidgetItem(purchase.purchase_number or ""))

                # المورد
                supplier_name = ""
                if purchase.supplier:
                    supplier_name = purchase.supplier.name
                self.purchases_table.setItem(row, 1, QTableWidgetItem(supplier_name))

                # التاريخ
                date_str = purchase.date.strftime("%Y-%m-%d") if purchase.date else ""
                self.purchases_table.setItem(row, 2, QTableWidgetItem(date_str))

                # المبلغ الإجمالي
                self.purchases_table.setItem(row, 3, QTableWidgetItem(format_currency(purchase.total_amount)))

                # المبلغ المدفوع
                self.purchases_table.setItem(row, 4, QTableWidgetItem(format_currency(purchase.paid_amount)))

                # الحالة
                statuses = {
                    'pending': 'في الانتظار',
                    'partially_received': 'مستلم جزئياً',
                    'completed': 'مستلم بالكامل',
                    'cancelled': 'ملغي'
                }
                status_text = statuses.get(purchase.status, purchase.status or "")
                self.purchases_table.setItem(row, 5, QTableWidgetItem(status_text))

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير المشتريات: {str(e)}")

    def generate_profit_loss_report(self):
        """إنتاج تقرير الأرباح والخسائر"""
        try:
            # الحصول على الفلاتر
            start_date = qdate_to_datetime(self.profit_loss_start_date.date())
            end_date = qdate_to_datetime(self.profit_loss_end_date.date())

            # حساب إجمالي المبيعات والأرباح
            sales = self.session.query(Sale).filter(
                Sale.date >= start_date,
                Sale.date <= end_date,
                Sale.status == 'completed'
            ).all()

            total_sales_revenue = sum(sale.total_amount for sale in sales)
            total_sales_cost = 0
            total_sales_profit = 0

            # حساب تكلفة المبيعات والأرباح
            for sale in sales:
                sale_items = self.session.query(SaleItem).filter(SaleItem.sale_id == sale.id).all()
                for item in sale_items:
                    inventory_item = self.session.query(Inventory).get(item.inventory_id)
                    if inventory_item:
                        item_cost = inventory_item.cost_price * item.quantity
                        total_sales_cost += item_cost
                        total_sales_profit += (item.unit_price * item.quantity) - item_cost

            # حساب إجمالي المشتريات
            purchases = self.session.query(Purchase).filter(
                Purchase.date >= start_date,
                Purchase.date <= end_date
            ).all()

            total_purchases = sum(purchase.total_amount for purchase in purchases)

            # حساب صافي الربح/الخسارة
            net_profit_loss = total_sales_profit

            # إنشاء تقرير HTML
            html_report = f"""
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; direction: rtl; }}
                    .header {{ text-align: center; color: #2c3e50; margin-bottom: 20px; }}
                    .section {{ margin: 15px 0; padding: 10px; border: 1px solid #bdc3c7; border-radius: 5px; }}
                    .positive {{ color: #27ae60; font-weight: bold; }}
                    .negative {{ color: #e74c3c; font-weight: bold; }}
                    .neutral {{ color: #34495e; font-weight: bold; }}
                    .total {{ font-size: 18px; background-color: #ecf0f1; padding: 10px; border-radius: 5px; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h2>تقرير الأرباح والخسائر</h2>
                    <p>من {start_date.strftime('%Y-%m-%d')} إلى {end_date.strftime('%Y-%m-%d')}</p>
                </div>

                <div class="section">
                    <h3>الإيرادات</h3>
                    <p>إجمالي المبيعات: <span class="positive">{format_currency(total_sales_revenue)}</span></p>
                    <p>عدد فواتير المبيعات: <span class="neutral">{len(sales)}</span></p>
                </div>

                <div class="section">
                    <h3>التكاليف</h3>
                    <p>تكلفة البضاعة المباعة: <span class="negative">{format_currency(total_sales_cost)}</span></p>
                    <p>إجمالي المشتريات: <span class="negative">{format_currency(total_purchases)}</span></p>
                    <p>عدد أوامر الشراء: <span class="neutral">{len(purchases)}</span></p>
                </div>

                <div class="section">
                    <h3>الأرباح</h3>
                    <p>إجمالي ربح المبيعات: <span class="positive">{format_currency(total_sales_profit)}</span></p>
                    <p>هامش الربح: <span class="neutral">{(total_sales_profit / total_sales_revenue * 100) if total_sales_revenue > 0 else 0:.2f}%</span></p>
                </div>

                <div class="section total">
                    <h3>النتيجة النهائية</h3>
                    <p>صافي الربح/الخسارة: <span class="{'positive' if net_profit_loss >= 0 else 'negative'}">{format_currency(net_profit_loss)}</span></p>
                </div>
            </body>
            </html>
            """

            self.profit_loss_summary.setHtml(html_report)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير الأرباح والخسائر: {str(e)}")

    def generate_inventory_report(self):
        """إنتاج تقرير المخزون"""
        try:
            # الحصول على الفلاتر
            category = self.inventory_category_combo.currentData()
            supplier_id = self.inventory_supplier_combo.currentData()
            status = self.inventory_status_combo.currentData()

            # بناء الاستعلام
            query = self.session.query(Inventory)

            # تطبيق الفلاتر
            if category:
                query = query.filter(Inventory.category == category)
            if supplier_id:
                query = query.filter(Inventory.supplier_id == supplier_id)

            # تطبيق فلتر حالة المخزون
            if status == "low_stock":
                query = query.filter(Inventory.quantity <= Inventory.min_quantity)
            elif status == "out_of_stock":
                query = query.filter(Inventory.quantity == 0)
            elif status == "in_stock":
                query = query.filter(Inventory.quantity > 0)

            # تنفيذ الاستعلام
            inventory_items = query.order_by(Inventory.name).all()

            # حساب الإحصائيات
            total_items = len(inventory_items)
            total_value = sum(item.cost_price * item.quantity for item in inventory_items)
            low_stock_items = len([item for item in inventory_items if item.quantity <= item.min_quantity])

            # تحديث الملخص
            self.inventory_count_label.label.setText(f"عدد العناصر: {total_items}")
            self.inventory_value_label.label.setText(f"قيمة المخزون: {format_currency(total_value)}")
            self.inventory_low_stock_label.label.setText(f"عناصر منخفضة المخزون: {low_stock_items}")

            # تحديث الجدول
            self.inventory_table.setRowCount(len(inventory_items))
            for row, item in enumerate(inventory_items):
                # الاسم
                self.inventory_table.setItem(row, 0, QTableWidgetItem(item.name or ""))

                # الفئة
                self.inventory_table.setItem(row, 1, QTableWidgetItem(item.category or ""))

                # الوحدة
                self.inventory_table.setItem(row, 2, QTableWidgetItem(item.unit or ""))

                # الكمية
                quantity_item = QTableWidgetItem(str(int(item.quantity)))
                if item.quantity <= item.min_quantity:
                    quantity_item.setBackground(QColor(255, 235, 235))  # خلفية حمراء فاتحة
                self.inventory_table.setItem(row, 3, quantity_item)

                # الحد الأدنى
                self.inventory_table.setItem(row, 4, QTableWidgetItem(str(int(item.min_quantity))))

                # سعر التكلفة
                self.inventory_table.setItem(row, 5, QTableWidgetItem(format_currency(item.cost_price)))

                # سعر البيع
                self.inventory_table.setItem(row, 6, QTableWidgetItem(format_currency(item.selling_price)))

                # المورد
                supplier_name = ""
                if item.supplier:
                    supplier_name = item.supplier.name
                self.inventory_table.setItem(row, 7, QTableWidgetItem(supplier_name))

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير المخزون: {str(e)}")

    def generate_clients_report(self):
        """إنتاج تقرير العملاء المتطور"""
        try:
            # الحصول على الفلاتر
            client_id = self.clients_client_combo.currentData()

            if client_id:
                # تقرير عميل محدد
                client = self.session.query(Client).get(client_id)
                if not client:
                    show_error_message("خطأ", "العميل المحدد غير موجود")
                    return

                # الحصول على مبيعات العميل
                sales = self.session.query(Sale).filter(Sale.client_id == client_id).order_by(Sale.date.desc()).all()

                # حساب الإحصائيات المتقدمة
                total_sales = sum(sale.total_amount for sale in sales)
                total_paid = sum(sale.paid_amount for sale in sales)
                remaining_amount = total_sales - total_paid

                # حساب إحصائيات إضافية
                completed_sales = [s for s in sales if s.status == 'completed']
                pending_sales = [s for s in sales if s.status == 'pending']
                avg_sale_amount = total_sales / len(sales) if sales else 0

                # تحديث الملخص الإحصائي
                self.clients_count_label.label.setText(f"عدد العملاء: 1")
                self.clients_total_sales_label.label.setText(f"إجمالي المبيعات: {format_currency(total_sales)}")
                self.clients_avg_sales_label.label.setText(f"متوسط المبيعات: {format_currency(avg_sale_amount)}")

                # إنشاء تقرير HTML متطور
                html_report = f"""
                <html>
                <head>
                    <style>
                        body {{
                            font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                            direction: rtl;
                            background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
                            margin: 0;
                            padding: 20px;
                        }}
                        .header {{
                            text-align: center;
                            color: #e91e63;
                            margin-bottom: 30px;
                            background: white;
                            padding: 20px;
                            border-radius: 15px;
                            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.2);
                        }}
                        .info-grid {{
                            display: grid;
                            grid-template-columns: 1fr 1fr;
                            gap: 15px;
                            margin: 20px 0;
                        }}
                        .info-card {{
                            background: white;
                            padding: 15px;
                            border-radius: 12px;
                            border-left: 5px solid #e91e63;
                            box-shadow: 0 2px 10px rgba(233, 30, 99, 0.1);
                        }}
                        .positive {{ color: #27ae60; font-weight: bold; font-size: 16px; }}
                        .negative {{ color: #e74c3c; font-weight: bold; font-size: 16px; }}
                        .neutral {{ color: #e91e63; font-weight: bold; font-size: 16px; }}
                        .stats-section {{
                            background: white;
                            padding: 20px;
                            border-radius: 15px;
                            margin: 20px 0;
                            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.2);
                        }}
                        .stat-item {{
                            display: flex;
                            justify-content: space-between;
                            padding: 10px 0;
                            border-bottom: 1px solid #fce4ec;
                        }}
                        .stat-label {{ font-weight: 600; color: #2c3e50; }}
                        .stat-value {{ font-weight: bold; }}
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>🤝 تقرير العميل المتطور</h1>
                        <h2 style="color: #2c3e50; margin: 10px 0;">{client.name}</h2>
                        <p style="color: #666; font-size: 14px;">تقرير شامل لأداء العميل ومعاملاته</p>
                    </div>

                    <div class="info-grid">
                        <div class="info-card">
                            <h3 style="color: #e91e63; margin-top: 0;">📞 معلومات الاتصال</h3>
                            <p><strong>الهاتف:</strong> {client.phone or 'غير محدد'}</p>
                            <p><strong>العنوان:</strong> {client.address or 'غير محدد'}</p>
                        </div>

                        <div class="info-card">
                            <h3 style="color: #e91e63; margin-top: 0;">📊 إحصائيات سريعة</h3>
                            <p><strong>عدد الفواتير:</strong> <span class="neutral">{len(sales)}</span></p>
                            <p><strong>الفواتير المكتملة:</strong> <span class="positive">{len(completed_sales)}</span></p>
                            <p><strong>الفواتير المعلقة:</strong> <span class="{'negative' if len(pending_sales) > 0 else 'positive'}">{len(pending_sales)}</span></p>
                        </div>
                    </div>

                    <div class="stats-section">
                        <h3 style="color: #e91e63; margin-top: 0;">💰 التحليل المالي المتقدم</h3>
                        <div class="stat-item">
                            <span class="stat-label">إجمالي المبيعات:</span>
                            <span class="stat-value positive">{format_currency(total_sales)}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">إجمالي المدفوعات:</span>
                            <span class="stat-value positive">{format_currency(total_paid)}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">المبلغ المتبقي:</span>
                            <span class="stat-value {'negative' if remaining_amount > 0 else 'positive'}">{format_currency(remaining_amount)}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">متوسط قيمة الفاتورة:</span>
                            <span class="stat-value neutral">{format_currency(avg_sale_amount)}</span>
                        </div>
                        <div class="stat-item" style="border-bottom: none;">
                            <span class="stat-label">نسبة السداد:</span>
                            <span class="stat-value {'positive' if total_sales > 0 and (total_paid/total_sales) > 0.8 else 'negative'}">{(total_paid/total_sales*100) if total_sales > 0 else 0:.1f}%</span>
                        </div>
                    </div>
                </body>
                </html>
                """

                self.clients_summary.setHtml(html_report)

                # تحديث جدول المبيعات
                self.clients_sales_table.setRowCount(len(sales))
                for row, sale in enumerate(sales):
                    # رقم الفاتورة
                    self.clients_sales_table.setItem(row, 0, QTableWidgetItem(sale.sale_number or ""))

                    # التاريخ
                    date_str = sale.date.strftime("%Y-%m-%d") if sale.date else ""
                    self.clients_sales_table.setItem(row, 1, QTableWidgetItem(date_str))

                    # المبلغ الإجمالي
                    self.clients_sales_table.setItem(row, 2, QTableWidgetItem(format_currency(sale.total_amount)))

                    # المبلغ المدفوع
                    self.clients_sales_table.setItem(row, 3, QTableWidgetItem(format_currency(sale.paid_amount)))

                    # طريقة الدفع
                    payment_methods = {
                        'cash': 'نقدي',
                        'credit': 'آجل',
                        'bank_transfer': 'تحويل بنكي',
                        'check': 'شيك'
                    }
                    payment_method = payment_methods.get(sale.payment_method, sale.payment_method or "")
                    self.clients_sales_table.setItem(row, 4, QTableWidgetItem(payment_method))

                    # الحالة
                    statuses = {
                        'pending': 'في الانتظار',
                        'completed': 'مكتملة',
                        'cancelled': 'ملغية',
                        'returned': 'مرتجعة'
                    }
                    status_text = statuses.get(sale.status, sale.status or "")
                    self.clients_sales_table.setItem(row, 5, QTableWidgetItem(status_text))

            else:
                # تقرير جميع العملاء المتطور
                clients = self.session.query(Client).order_by(Client.name).all()

                # حساب الإحصائيات الإجمالية
                total_clients = len(clients)
                grand_total_sales = 0
                grand_total_paid = 0
                grand_remaining = 0

                clients_data = []
                for client in clients:
                    sales = self.session.query(Sale).filter(Sale.client_id == client.id).all()
                    total_sales = sum(sale.total_amount for sale in sales)
                    total_paid = sum(sale.paid_amount for sale in sales)
                    remaining_amount = total_sales - total_paid

                    grand_total_sales += total_sales
                    grand_total_paid += total_paid
                    grand_remaining += remaining_amount

                    clients_data.append({
                        'name': client.name,
                        'phone': client.phone or 'غير محدد',
                        'invoices_count': len(sales),
                        'total_sales': total_sales,
                        'total_paid': total_paid,
                        'remaining': remaining_amount
                    })

                # تحديث الملخص الإحصائي
                self.clients_count_label.label.setText(f"عدد العملاء: {total_clients}")
                self.clients_total_sales_label.label.setText(f"إجمالي المبيعات: {format_currency(grand_total_sales)}")
                avg_sales = grand_total_sales / total_clients if total_clients > 0 else 0
                self.clients_avg_sales_label.label.setText(f"متوسط المبيعات: {format_currency(avg_sales)}")

                # إنشاء تقرير HTML متطور
                html_report = f"""
                <html>
                <head>
                    <style>
                        body {{
                            font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                            direction: rtl;
                            background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
                            margin: 0;
                            padding: 20px;
                        }}
                        .header {{
                            text-align: center;
                            color: #e91e63;
                            margin-bottom: 30px;
                            background: white;
                            padding: 20px;
                            border-radius: 15px;
                            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.2);
                        }}
                        .summary-cards {{
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                            gap: 15px;
                            margin: 20px 0;
                        }}
                        .summary-card {{
                            background: white;
                            padding: 20px;
                            border-radius: 12px;
                            border-left: 5px solid #e91e63;
                            box-shadow: 0 2px 10px rgba(233, 30, 99, 0.1);
                            text-align: center;
                        }}
                        .card-title {{ color: #2c3e50; font-size: 14px; margin-bottom: 10px; }}
                        .card-value {{ color: #e91e63; font-size: 24px; font-weight: bold; }}
                        table {{
                            width: 100%;
                            border-collapse: collapse;
                            margin: 20px 0;
                            background: white;
                            border-radius: 15px;
                            overflow: hidden;
                            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.2);
                        }}
                        th {{
                            background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%);
                            color: white;
                            padding: 15px 10px;
                            font-weight: bold;
                            font-size: 14px;
                        }}
                        td {{
                            padding: 12px 10px;
                            text-align: center;
                            border-bottom: 1px solid rgba(233, 30, 99, 0.1);
                        }}
                        tr:nth-child(even) {{ background-color: #fce4ec; }}
                        tr:hover {{ background-color: rgba(233, 30, 99, 0.1); }}
                        .positive {{ color: #27ae60; font-weight: bold; }}
                        .negative {{ color: #e74c3c; font-weight: bold; }}
                        .neutral {{ color: #e91e63; font-weight: bold; }}
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>🤝 تقرير جميع العملاء المتطور</h1>
                        <p style="color: #666; font-size: 14px;">تقرير شامل لجميع العملاء ومعاملاتهم</p>
                    </div>

                    <div class="summary-cards">
                        <div class="summary-card">
                            <div class="card-title">إجمالي العملاء</div>
                            <div class="card-value">{total_clients}</div>
                        </div>
                        <div class="summary-card">
                            <div class="card-title">إجمالي المبيعات</div>
                            <div class="card-value">{format_currency(grand_total_sales)}</div>
                        </div>
                        <div class="summary-card">
                            <div class="card-title">إجمالي المدفوعات</div>
                            <div class="card-value">{format_currency(grand_total_paid)}</div>
                        </div>
                        <div class="summary-card">
                            <div class="card-title">المبلغ المتبقي</div>
                            <div class="card-value {'negative' if grand_remaining > 0 else 'positive'}">{format_currency(grand_remaining)}</div>
                        </div>
                    </div>

                    <table>
                        <tr>
                            <th>اسم العميل</th>
                            <th>الهاتف</th>
                            <th>عدد الفواتير</th>
                            <th>إجمالي المبيعات</th>
                            <th>إجمالي المدفوعات</th>
                            <th>المبلغ المتبقي</th>
                        </tr>
                """

                for client_data in clients_data:
                    html_report += f"""
                        <tr>
                            <td style="font-weight: 600; color: #2c3e50;">{client_data['name']}</td>
                            <td>{client_data['phone']}</td>
                            <td class="neutral">{client_data['invoices_count']}</td>
                            <td class="positive">{format_currency(client_data['total_sales'])}</td>
                            <td class="positive">{format_currency(client_data['total_paid'])}</td>
                            <td class="{'negative' if client_data['remaining'] > 0 else 'positive'}">{format_currency(client_data['remaining'])}</td>
                        </tr>
                    """

                html_report += """
                    </table>
                </body>
                </html>
                """

                self.clients_summary.setHtml(html_report)

                # إخفاء جدول المبيعات
                self.clients_sales_table.setRowCount(0)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير العملاء: {str(e)}")

    def setup_suppliers_tab(self):
        """إعداد تبويب تقرير الموردين"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير مع تصميم محسن
        filter_group = QGroupBox("🏭 فلاتر تقرير الموردين")
        filter_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 3px solid #8e44ad;
                border-radius: 15px;
                margin-top: 10px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f4f1fb,
                    stop:0.7 #e8daef,
                    stop:1 #d7bde2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 5px 15px;
                background-color: #8e44ad;
                color: white;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        filter_layout = QFormLayout()
        filter_layout.setSpacing(12)
        filter_layout.setContentsMargins(20, 25, 20, 15)

        # فلتر المورد مع تصميم محسن
        supplier_label = QLabel("🏭 المورد:")
        supplier_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)

        self.suppliers_supplier_combo = QComboBox()
        self.suppliers_supplier_combo.addItem("جميع الموردين", None)
        suppliers = self.session.query(Supplier).order_by(Supplier.name).all()
        for supplier in suppliers:
            self.suppliers_supplier_combo.addItem(supplier.name, supplier.id)
        self.suppliers_supplier_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #8e44ad;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QComboBox:hover {
                border: 2px solid #7d3c98;
                background-color: #f8f9fa;
            }
            QComboBox:focus {
                border: 2px solid #1abc9c;
                background-color: #ffffff;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 5px solid transparent;
                border-top: 8px solid #8e44ad;
                margin-right: 5px;
            }
        """)
        filter_layout.addRow(supplier_label, self.suppliers_supplier_combo)

        # أزرار التحكم مطابقة لباقي البرنامج
        buttons_layout = QHBoxLayout()

        self.suppliers_apply_button = QPushButton("🏭 تطبيق التقرير")
        self.style_advanced_button(self.suppliers_apply_button, 'primary')
        self.suppliers_apply_button.clicked.connect(self.generate_suppliers_report)

        self.suppliers_export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.suppliers_export_button, 'info')

        # إنشاء قائمة منسدلة للتصدير
        suppliers_export_menu = QMenu(self)
        suppliers_export_menu.setStyleSheet("QMenu { background-color: white; border: 1px solid #E2E8F0; }")

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(lambda: self.export_suppliers_to_pdf())
        suppliers_export_menu.addAction(pdf_action)

        html_action = QAction("🌐 حفظ كـ HTML", self)
        html_action.triggered.connect(lambda: self.save_suppliers_html())
        suppliers_export_menu.addAction(html_action)

        self.suppliers_export_button.setMenu(suppliers_export_menu)

        self.suppliers_refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.suppliers_refresh_button, 'modern_teal')
        self.suppliers_refresh_button.clicked.connect(self.generate_suppliers_report)

        buttons_layout.addWidget(self.suppliers_apply_button)
        buttons_layout.addWidget(self.suppliers_export_button)
        buttons_layout.addWidget(self.suppliers_refresh_button)
        buttons_layout.addStretch()

        filter_layout.addRow("", buttons_layout)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير مع تصميم محسن
        results_group = QGroupBox("🏭 نتائج تقرير الموردين")
        results_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 3px solid #8e44ad;
                border-radius: 15px;
                margin-top: 10px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f4f1fb,
                    stop:0.7 #e8daef,
                    stop:1 #d7bde2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 5px 15px;
                background-color: #8e44ad;
                color: white;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        results_layout = QVBoxLayout()
        results_layout.setContentsMargins(20, 25, 20, 15)
        results_layout.setSpacing(15)

        # تقرير الموردين مع تصميم محسن
        self.suppliers_summary = QTextBrowser()
        self.suppliers_summary.setMinimumHeight(300)
        self.suppliers_summary.setStyleSheet("""
            QTextBrowser {
                border: 3px solid #8e44ad;
                border-radius: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f4f1fb,
                    stop:0.7 #e8daef,
                    stop:1 #d7bde2);
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                font-size: 14px;
                color: #2c3e50;
                padding: 20px;
                line-height: 1.6;
            }
            QScrollBar:vertical {
                border: 2px solid #8e44ad;
                background: #f4f1fb;
                width: 15px;
                border-radius: 7px;
            }
            QScrollBar::handle:vertical {
                background: #8e44ad;
                border-radius: 7px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #7d3c98;
            }
        """)

        # جدول مشتريات المورد مطابق لباقي البرنامج
        self.suppliers_purchases_table = QTableWidget()
        self.suppliers_purchases_table.setColumnCount(5)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "🧾 رقم أمر الشراء",
            "📅 التاريخ",
            "💰 المبلغ الإجمالي",
            "💵 المبلغ المدفوع",
            "🎯 الحالة"
        ]
        self.suppliers_purchases_table.setHorizontalHeaderLabels(headers)

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للفواتير والمصروفات والإيرادات
        self.suppliers_purchases_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للفواتير والمصروفات والإيرادات
        header = self.suppliers_purchases_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للفواتير والمصروفات والإيرادات
        self.suppliers_purchases_table.verticalHeader().setDefaultSectionSize(45)
        self.suppliers_purchases_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        self.suppliers_purchases_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.suppliers_purchases_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.suppliers_purchases_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.suppliers_purchases_table.setAlternatingRowColors(True)

        results_layout.addWidget(self.suppliers_summary)
        results_layout.addWidget(QLabel("مشتريات المورد:"))
        results_layout.addWidget(self.suppliers_purchases_table)

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        self.suppliers_tab.setLayout(layout)

    def generate_suppliers_report(self):
        """إنتاج تقرير الموردين"""
        try:
            # الحصول على الفلاتر
            supplier_id = self.suppliers_supplier_combo.currentData()

            if supplier_id:
                # تقرير مورد محدد
                supplier = self.session.query(Supplier).get(supplier_id)
                if not supplier:
                    show_error_message("خطأ", "المورد المحدد غير موجود")
                    return

                # الحصول على مشتريات المورد
                purchases = self.session.query(Purchase).filter(Purchase.supplier_id == supplier_id).order_by(Purchase.date.desc()).all()

                # حساب الإحصائيات
                total_purchases = sum(purchase.total_amount for purchase in purchases)
                total_paid = sum(purchase.paid_amount for purchase in purchases)
                remaining_amount = total_purchases - total_paid

                # إنشاء تقرير HTML
                html_report = f"""
                <html>
                <head>
                    <style>
                        body {{ font-family: Arial, sans-serif; direction: rtl; }}
                        .header {{ text-align: center; color: #2c3e50; margin-bottom: 20px; }}
                        .info {{ margin: 10px 0; }}
                        .positive {{ color: #27ae60; font-weight: bold; }}
                        .negative {{ color: #e74c3c; font-weight: bold; }}
                        .neutral {{ color: #34495e; font-weight: bold; }}
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h2>تقرير المورد: {supplier.name}</h2>
                    </div>
                    <div class="info">
                        <p><strong>الهاتف:</strong> {supplier.phone or 'غير محدد'}</p>
                        <p><strong>العنوان:</strong> {supplier.address or 'غير محدد'}</p>
                        <p><strong>عدد أوامر الشراء:</strong> <span class="neutral">{len(purchases)}</span></p>
                        <p><strong>إجمالي المشتريات:</strong> <span class="negative">{format_currency(total_purchases)}</span></p>
                        <p><strong>إجمالي المدفوعات:</strong> <span class="positive">{format_currency(total_paid)}</span></p>
                        <p><strong>المبلغ المتبقي:</strong> <span class="{'negative' if remaining_amount > 0 else 'positive'}">{format_currency(remaining_amount)}</span></p>
                    </div>
                </body>
                </html>
                """

                self.suppliers_summary.setHtml(html_report)

                # تحديث جدول المشتريات
                self.suppliers_purchases_table.setRowCount(len(purchases))
                for row, purchase in enumerate(purchases):
                    # رقم أمر الشراء
                    self.suppliers_purchases_table.setItem(row, 0, QTableWidgetItem(purchase.purchase_number or ""))

                    # التاريخ
                    date_str = purchase.date.strftime("%Y-%m-%d") if purchase.date else ""
                    self.suppliers_purchases_table.setItem(row, 1, QTableWidgetItem(date_str))

                    # المبلغ الإجمالي
                    self.suppliers_purchases_table.setItem(row, 2, QTableWidgetItem(format_currency(purchase.total_amount)))

                    # المبلغ المدفوع
                    self.suppliers_purchases_table.setItem(row, 3, QTableWidgetItem(format_currency(purchase.paid_amount)))

                    # الحالة
                    statuses = {
                        'pending': 'في الانتظار',
                        'partially_received': 'مستلم جزئياً',
                        'completed': 'مستلم بالكامل',
                        'cancelled': 'ملغي'
                    }
                    status_text = statuses.get(purchase.status, purchase.status or "")
                    self.suppliers_purchases_table.setItem(row, 4, QTableWidgetItem(status_text))

            else:
                # تقرير جميع الموردين
                suppliers = self.session.query(Supplier).order_by(Supplier.name).all()

                # إنشاء تقرير HTML
                html_report = """
                <html>
                <head>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        .header { text-align: center; color: #2c3e50; margin-bottom: 20px; }
                        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
                        th, td { border: 1px solid #bdc3c7; padding: 8px; text-align: center; }
                        th { background-color: #34495e; color: white; }
                        .positive { color: #27ae60; font-weight: bold; }
                        .negative { color: #e74c3c; font-weight: bold; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h2>تقرير جميع الموردين</h2>
                    </div>
                    <table>
                        <tr>
                            <th>اسم المورد</th>
                            <th>عدد أوامر الشراء</th>
                            <th>إجمالي المشتريات</th>
                            <th>إجمالي المدفوعات</th>
                            <th>المبلغ المتبقي</th>
                        </tr>
                """

                for supplier in suppliers:
                    purchases = self.session.query(Purchase).filter(Purchase.supplier_id == supplier.id).all()
                    total_purchases = sum(purchase.total_amount for purchase in purchases)
                    total_paid = sum(purchase.paid_amount for purchase in purchases)
                    remaining_amount = total_purchases - total_paid

                    html_report += f"""
                        <tr>
                            <td>{supplier.name}</td>
                            <td>{len(purchases)}</td>
                            <td class="negative">{format_currency(total_purchases)}</td>
                            <td class="positive">{format_currency(total_paid)}</td>
                            <td class="{'negative' if remaining_amount > 0 else 'positive'}">{format_currency(remaining_amount)}</td>
                        </tr>
                    """

                html_report += """
                    </table>
                </body>
                </html>
                """

                self.suppliers_summary.setHtml(html_report)

                # إخفاء جدول المشتريات
                self.suppliers_purchases_table.setRowCount(0)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير الموردين: {str(e)}")

    def setup_expenses_tab(self):
        """إعداد تبويب تقرير المصروفات"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير مع تصميم محسن
        filter_group = QGroupBox("💸 فلاتر تقرير المصروفات")
        filter_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 3px solid #e74c3c;
                border-radius: 15px;
                margin-top: 10px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #fdf2f2,
                    stop:0.7 #fadbd8,
                    stop:1 #f5b7b1);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 5px 15px;
                background-color: #e74c3c;
                color: white;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        filter_layout = QFormLayout()
        filter_layout.setSpacing(12)
        filter_layout.setContentsMargins(20, 25, 20, 15)

        # فلتر الفترة الزمنية مع تصميم محسن
        period_label = QLabel("📅 الفترة الزمنية:")
        period_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)

        self.expenses_period_combo = QComboBox()
        self.expenses_period_combo.addItems(["اليوم", "الأسبوع الحالي", "الشهر الحالي", "الربع الحالي", "السنة الحالية", "فترة محددة"])
        self.expenses_period_combo.currentIndexChanged.connect(self.on_expenses_period_changed)
        self.expenses_period_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #e74c3c;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QComboBox:hover {
                border: 2px solid #c0392b;
                background-color: #f8f9fa;
            }
            QComboBox:focus {
                border: 2px solid #1abc9c;
                background-color: #ffffff;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 5px solid transparent;
                border-top: 8px solid #e74c3c;
                margin-right: 5px;
            }
        """)
        filter_layout.addRow(period_label, self.expenses_period_combo)

        # حقول تاريخ البداية والنهاية مع تصميم محسن
        date_layout = QHBoxLayout()
        date_layout.setSpacing(15)

        # تسمية وحقل تاريخ البداية
        start_label = QLabel("📅 من:")
        start_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                font-weight: bold;
                color: #27ae60;
                padding: 5px;
            }
        """)

        self.expenses_start_date = QDateEdit()
        self.expenses_start_date.setCalendarPopup(True)
        self.expenses_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.expenses_start_date.setEnabled(False)
        self.expenses_start_date.setStyleSheet("""
            QDateEdit {
                border: 2px solid #27ae60;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QDateEdit:hover {
                border: 2px solid #229954;
                background-color: #f8f9fa;
            }
            QDateEdit:disabled {
                background-color: #ecf0f1;
                color: #7f8c8d;
                border: 2px solid #bdc3c7;
            }
        """)

        # تسمية وحقل تاريخ النهاية
        end_label = QLabel("📅 إلى:")
        end_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                font-weight: bold;
                color: #e74c3c;
                padding: 5px;
            }
        """)

        self.expenses_end_date = QDateEdit()
        self.expenses_end_date.setCalendarPopup(True)
        self.expenses_end_date.setDate(QDate.currentDate())
        self.expenses_end_date.setEnabled(False)
        self.expenses_end_date.setStyleSheet("""
            QDateEdit {
                border: 2px solid #e74c3c;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QDateEdit:hover {
                border: 2px solid #c0392b;
                background-color: #f8f9fa;
            }
            QDateEdit:disabled {
                background-color: #ecf0f1;
                color: #7f8c8d;
                border: 2px solid #bdc3c7;
            }
        """)

        date_layout.addWidget(start_label)
        date_layout.addWidget(self.expenses_start_date)
        date_layout.addWidget(end_label)
        date_layout.addWidget(self.expenses_end_date)
        date_layout.addStretch()

        filter_layout.addRow("", date_layout)

        # فلتر نوع المصروف مع تصميم محسن
        category_label = QLabel("📂 نوع المصروف:")
        category_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)

        self.expenses_category_combo = QComboBox()
        self.expenses_category_combo.addItem("جميع الأنواع", None)
        # سنضيف الفئات من قاعدة البيانات
        try:
            categories = self.session.query(Expense.category).distinct().all()
            for category in categories:
                if category[0]:
                    self.expenses_category_combo.addItem(category[0], category[0])
        except:
            pass
        self.expenses_category_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #9b59b6;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QComboBox:hover {
                border: 2px solid #8e44ad;
                background-color: #f8f9fa;
            }
            QComboBox:focus {
                border: 2px solid #1abc9c;
                background-color: #ffffff;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 5px solid transparent;
                border-top: 8px solid #9b59b6;
                margin-right: 5px;
            }
        """)
        filter_layout.addRow(category_label, self.expenses_category_combo)

        # أزرار التحكم مطابقة لباقي البرنامج
        buttons_layout = QHBoxLayout()

        self.expenses_apply_button = QPushButton("💸 تطبيق التقرير")
        self.style_advanced_button(self.expenses_apply_button, 'danger')
        self.expenses_apply_button.clicked.connect(self.generate_expenses_report)

        self.expenses_export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.expenses_export_button, 'info')

        # إنشاء قائمة منسدلة للتصدير
        expenses_export_menu = QMenu(self)
        expenses_export_menu.setStyleSheet("QMenu { background-color: white; border: 1px solid #E2E8F0; }")

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(lambda: self.export_to_csv(self.expenses_table, "تقرير_المصروفات"))
        expenses_export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(lambda: self.print_table(self.expenses_table, "تقرير المصروفات"))
        expenses_export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(lambda: self.export_to_csv(self.expenses_table, "تقرير_المصروفات"))
        expenses_export_menu.addAction(csv_action)

        self.expenses_export_button.setMenu(expenses_export_menu)

        self.expenses_refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.expenses_refresh_button, 'modern_teal')
        self.expenses_refresh_button.clicked.connect(self.generate_expenses_report)

        buttons_layout.addWidget(self.expenses_apply_button)
        buttons_layout.addWidget(self.expenses_export_button)
        buttons_layout.addWidget(self.expenses_refresh_button)
        buttons_layout.addStretch()

        filter_layout.addRow("", buttons_layout)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير مع تصميم محسن
        results_group = QGroupBox("💸 نتائج تقرير المصروفات")
        results_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 3px solid #e74c3c;
                border-radius: 15px;
                margin-top: 10px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #fdf2f2,
                    stop:0.7 #fadbd8,
                    stop:1 #f5b7b1);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 5px 15px;
                background-color: #e74c3c;
                color: white;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        results_layout = QVBoxLayout()
        results_layout.setContentsMargins(20, 25, 20, 15)
        results_layout.setSpacing(15)

        # ملخص المصروفات مع تصميم محسن
        summary_layout = QHBoxLayout()
        summary_layout.setSpacing(20)

        # عدد المصروفات
        self.expenses_count_label = QLabel("💸 عدد المصروفات: 0")
        self.expenses_count_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #e74c3c,
                    stop:1 #c0392b);
                color: white;
                border-radius: 12px;
                padding: 15px 20px;
                font-size: 14px;
                font-weight: bold;
                border: 2px solid #c0392b;
            }
        """)

        # إجمالي المصروفات
        self.expenses_total_label = QLabel("💰 إجمالي المصروفات: 0.00")
        self.expenses_total_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #d35400,
                    stop:1 #ba4a00);
                color: white;
                border-radius: 12px;
                padding: 15px 20px;
                font-size: 14px;
                font-weight: bold;
                border: 2px solid #ba4a00;
            }
        """)

        summary_layout.addWidget(self.expenses_count_label)
        summary_layout.addWidget(self.expenses_total_label)
        summary_layout.addStretch()

        # جدول المصروفات مع تصميم متطور
        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(5)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "📅 التاريخ",
            "📝 الوصف",
            "🏷️ النوع",
            "💸 المبلغ",
            "📋 الملاحظات"
        ]
        self.expenses_table.setHorizontalHeaderLabels(headers)

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للفواتير والمصروفات والإيرادات
        self.expenses_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للفواتير والمصروفات والإيرادات
        header = self.expenses_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للفواتير والمصروفات والإيرادات
        self.expenses_table.verticalHeader().setDefaultSectionSize(45)
        self.expenses_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        self.expenses_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.expenses_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.expenses_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.expenses_table.setAlternatingRowColors(True)

        results_layout.addLayout(summary_layout)
        results_layout.addWidget(self.expenses_table)

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        self.expenses_tab.setLayout(layout)

    def generate_expenses_report(self):
        """إنتاج تقرير المصروفات"""
        try:
            # الحصول على الفلاتر
            start_date = qdate_to_datetime(self.expenses_start_date.date())
            end_date = qdate_to_datetime(self.expenses_end_date.date())
            category = self.expenses_category_combo.currentData()

            # بناء الاستعلام
            query = self.session.query(Expense).filter(
                Expense.date >= start_date,
                Expense.date <= end_date
            )

            # تطبيق فلتر النوع
            if category:
                query = query.filter(Expense.category == category)

            # تنفيذ الاستعلام
            expenses = query.order_by(Expense.date.desc()).all()

            # حساب الإحصائيات
            total_expenses = sum(expense.amount for expense in expenses)

            # تحديث الملخص
            self.expenses_count_label.setText(f"💸 عدد المصروفات: {len(expenses)}")
            self.expenses_total_label.setText(f"💰 إجمالي المصروفات: {format_currency(total_expenses)}")

            # تحديث الجدول
            self.expenses_table.setRowCount(len(expenses))
            for row, expense in enumerate(expenses):
                # التاريخ
                date_str = expense.date.strftime("%Y-%m-%d") if expense.date else ""
                self.expenses_table.setItem(row, 0, QTableWidgetItem(date_str))

                # الوصف
                self.expenses_table.setItem(row, 1, QTableWidgetItem(expense.description or ""))

                # النوع
                self.expenses_table.setItem(row, 2, QTableWidgetItem(expense.category or ""))

                # المبلغ
                self.expenses_table.setItem(row, 3, QTableWidgetItem(format_currency(expense.amount)))

                # الملاحظات
                self.expenses_table.setItem(row, 4, QTableWidgetItem(expense.notes or ""))

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير المصروفات: {str(e)}")

    def setup_revenues_tab(self):
        """إعداد تبويب تقرير الإيرادات"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير مع تصميم محسن
        filter_group = QGroupBox("💰 فلاتر تقرير الإيرادات")
        filter_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 3px solid #27ae60;
                border-radius: 15px;
                margin-top: 10px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f1f8e9,
                    stop:0.7 #d5f4e6,
                    stop:1 #a9dfbf);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 5px 15px;
                background-color: #27ae60;
                color: white;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        filter_layout = QFormLayout()
        filter_layout.setSpacing(12)
        filter_layout.setContentsMargins(20, 25, 20, 15)

        # فلتر الفترة الزمنية مع تصميم محسن
        period_label = QLabel("📅 الفترة الزمنية:")
        period_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)

        self.revenues_period_combo = QComboBox()
        self.revenues_period_combo.addItems(["اليوم", "الأسبوع الحالي", "الشهر الحالي", "الربع الحالي", "السنة الحالية", "فترة محددة"])
        self.revenues_period_combo.currentIndexChanged.connect(self.on_revenues_period_changed)
        self.revenues_period_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #27ae60;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QComboBox:hover {
                border: 2px solid #229954;
                background-color: #f8f9fa;
            }
            QComboBox:focus {
                border: 2px solid #1abc9c;
                background-color: #ffffff;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 5px solid transparent;
                border-top: 8px solid #27ae60;
                margin-right: 5px;
            }
        """)
        filter_layout.addRow(period_label, self.revenues_period_combo)

        # حقول تاريخ البداية والنهاية مع تصميم محسن
        date_layout = QHBoxLayout()
        date_layout.setSpacing(15)

        # تسمية وحقل تاريخ البداية
        start_label = QLabel("📅 من:")
        start_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                font-weight: bold;
                color: #27ae60;
                padding: 5px;
            }
        """)

        self.revenues_start_date = QDateEdit()
        self.revenues_start_date.setCalendarPopup(True)
        self.revenues_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.revenues_start_date.setEnabled(False)
        self.revenues_start_date.setStyleSheet("""
            QDateEdit {
                border: 2px solid #27ae60;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QDateEdit:hover {
                border: 2px solid #229954;
                background-color: #f8f9fa;
            }
            QDateEdit:disabled {
                background-color: #ecf0f1;
                color: #7f8c8d;
                border: 2px solid #bdc3c7;
            }
        """)

        # تسمية وحقل تاريخ النهاية
        end_label = QLabel("📅 إلى:")
        end_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                font-weight: bold;
                color: #e74c3c;
                padding: 5px;
            }
        """)

        self.revenues_end_date = QDateEdit()
        self.revenues_end_date.setCalendarPopup(True)
        self.revenues_end_date.setDate(QDate.currentDate())
        self.revenues_end_date.setEnabled(False)
        self.revenues_end_date.setStyleSheet("""
            QDateEdit {
                border: 2px solid #e74c3c;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QDateEdit:hover {
                border: 2px solid #c0392b;
                background-color: #f8f9fa;
            }
            QDateEdit:disabled {
                background-color: #ecf0f1;
                color: #7f8c8d;
                border: 2px solid #bdc3c7;
            }
        """)

        date_layout.addWidget(start_label)
        date_layout.addWidget(self.revenues_start_date)
        date_layout.addWidget(end_label)
        date_layout.addWidget(self.revenues_end_date)
        date_layout.addStretch()

        filter_layout.addRow("", date_layout)

        # فلتر نوع الإيراد مع تصميم محسن
        category_label = QLabel("📂 نوع الإيراد:")
        category_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)

        self.revenues_category_combo = QComboBox()
        self.revenues_category_combo.addItem("جميع الأنواع", None)
        # سنضيف الفئات من قاعدة البيانات
        try:
            categories = self.session.query(Revenue.category).distinct().all()
            for category in categories:
                if category[0]:
                    self.revenues_category_combo.addItem(category[0], category[0])
        except:
            pass
        self.revenues_category_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #f39c12;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QComboBox:hover {
                border: 2px solid #e67e22;
                background-color: #f8f9fa;
            }
            QComboBox:focus {
                border: 2px solid #1abc9c;
                background-color: #ffffff;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 5px solid transparent;
                border-top: 8px solid #f39c12;
                margin-right: 5px;
            }
        """)
        filter_layout.addRow(category_label, self.revenues_category_combo)

        # أزرار التحكم مطابقة لباقي البرنامج
        buttons_layout = QHBoxLayout()

        self.revenues_apply_button = QPushButton("💰 تطبيق التقرير")
        self.style_advanced_button(self.revenues_apply_button, 'emerald')
        self.revenues_apply_button.clicked.connect(self.generate_revenues_report)

        self.revenues_export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.revenues_export_button, 'info')

        # إنشاء قائمة منسدلة للتصدير
        revenues_export_menu = QMenu(self)
        revenues_export_menu.setStyleSheet("QMenu { background-color: white; border: 1px solid #E2E8F0; }")

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(lambda: self.export_to_csv(self.revenues_table, "تقرير_الإيرادات"))
        revenues_export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(lambda: self.print_table(self.revenues_table, "تقرير الإيرادات"))
        revenues_export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(lambda: self.export_to_csv(self.revenues_table, "تقرير_الإيرادات"))
        revenues_export_menu.addAction(csv_action)

        self.revenues_export_button.setMenu(revenues_export_menu)

        self.revenues_refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.revenues_refresh_button, 'modern_teal')
        self.revenues_refresh_button.clicked.connect(self.generate_revenues_report)

        buttons_layout.addWidget(self.revenues_apply_button)
        buttons_layout.addWidget(self.revenues_export_button)
        buttons_layout.addWidget(self.revenues_refresh_button)
        buttons_layout.addStretch()

        filter_layout.addRow("", buttons_layout)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير مع تصميم محسن
        results_group = QGroupBox("💰 نتائج تقرير الإيرادات")
        results_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 3px solid #27ae60;
                border-radius: 15px;
                margin-top: 10px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f1f8e9,
                    stop:0.7 #d5f4e6,
                    stop:1 #a9dfbf);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 5px 15px;
                background-color: #27ae60;
                color: white;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        results_layout = QVBoxLayout()
        results_layout.setContentsMargins(20, 25, 20, 15)
        results_layout.setSpacing(15)

        # ملخص الإيرادات مع تصميم محسن
        summary_layout = QHBoxLayout()
        summary_layout.setSpacing(20)

        # عدد الإيرادات
        self.revenues_count_label = QLabel("💰 عدد الإيرادات: 0")
        self.revenues_count_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #27ae60,
                    stop:1 #229954);
                color: white;
                border-radius: 12px;
                padding: 15px 20px;
                font-size: 14px;
                font-weight: bold;
                border: 2px solid #229954;
            }
        """)

        # إجمالي الإيرادات
        self.revenues_total_label = QLabel("💵 إجمالي الإيرادات: 0.00")
        self.revenues_total_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f39c12,
                    stop:1 #e67e22);
                color: white;
                border-radius: 12px;
                padding: 15px 20px;
                font-size: 14px;
                font-weight: bold;
                border: 2px solid #e67e22;
            }
        """)

        summary_layout.addWidget(self.revenues_count_label)
        summary_layout.addWidget(self.revenues_total_label)
        summary_layout.addStretch()

        # جدول الإيرادات مع تصميم متطور
        self.revenues_table = QTableWidget()
        self.revenues_table.setColumnCount(5)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "📅 التاريخ",
            "📝 الوصف",
            "🏷️ النوع",
            "💰 المبلغ",
            "📋 الملاحظات"
        ]
        self.revenues_table.setHorizontalHeaderLabels(headers)

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للفواتير والمصروفات والإيرادات
        self.revenues_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للفواتير والمصروفات والإيرادات
        header = self.revenues_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للفواتير والمصروفات والإيرادات
        self.revenues_table.verticalHeader().setDefaultSectionSize(45)
        self.revenues_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        self.revenues_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.revenues_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.revenues_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.revenues_table.setAlternatingRowColors(True)

        results_layout.addLayout(summary_layout)
        results_layout.addWidget(self.revenues_table)

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        self.revenues_tab.setLayout(layout)

    def generate_revenues_report(self):
        """إنتاج تقرير الإيرادات"""
        try:
            # الحصول على الفلاتر
            start_date = qdate_to_datetime(self.revenues_start_date.date())
            end_date = qdate_to_datetime(self.revenues_end_date.date())
            category = self.revenues_category_combo.currentData()

            # بناء الاستعلام
            query = self.session.query(Revenue).filter(
                Revenue.date >= start_date,
                Revenue.date <= end_date
            )

            # تطبيق فلتر النوع
            if category:
                query = query.filter(Revenue.category == category)

            # تنفيذ الاستعلام
            revenues = query.order_by(Revenue.date.desc()).all()

            # حساب الإحصائيات
            total_revenues = sum(revenue.amount for revenue in revenues)

            # تحديث الملخص
            self.revenues_count_label.setText(f"💰 عدد الإيرادات: {len(revenues)}")
            self.revenues_total_label.setText(f"💵 إجمالي الإيرادات: {format_currency(total_revenues)}")

            # تحديث الجدول
            self.revenues_table.setRowCount(len(revenues))
            for row, revenue in enumerate(revenues):
                # التاريخ
                date_str = revenue.date.strftime("%Y-%m-%d") if revenue.date else ""
                self.revenues_table.setItem(row, 0, QTableWidgetItem(date_str))

                # الوصف
                self.revenues_table.setItem(row, 1, QTableWidgetItem(revenue.description or ""))

                # النوع
                self.revenues_table.setItem(row, 2, QTableWidgetItem(revenue.category or ""))

                # المبلغ
                self.revenues_table.setItem(row, 3, QTableWidgetItem(format_currency(revenue.amount)))

                # الملاحظات
                self.revenues_table.setItem(row, 4, QTableWidgetItem(revenue.notes or ""))

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير الإيرادات: {str(e)}")

    def setup_projects_tab(self):
        """إعداد تبويب تقرير المشاريع"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير مع تصميم محسن
        filter_group = QGroupBox("🏗️ فلاتر تقرير المشاريع")
        filter_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 3px solid #3498db;
                border-radius: 15px;
                margin-top: 10px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e3f2fd,
                    stop:1 #bbdefb);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 5px 15px;
                background-color: #3498db;
                color: white;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        filter_layout = QFormLayout()
        filter_layout.setSpacing(12)
        filter_layout.setContentsMargins(20, 25, 20, 15)

        # فلتر المشروع مع تصميم محسن
        project_label = QLabel("🏗️ المشروع:")
        project_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)

        self.projects_project_combo = QComboBox()
        self.projects_project_combo.addItem("جميع المشاريع", None)
        try:
            projects = self.session.query(Project).order_by(Project.name).all()
            for project in projects:
                self.projects_project_combo.addItem(project.name, project.id)
        except:
            pass
        self.projects_project_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #3498db;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QComboBox:hover {
                border: 2px solid #2980b9;
                background-color: #f8f9fa;
            }
            QComboBox:focus {
                border: 2px solid #1abc9c;
                background-color: #ffffff;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 5px solid transparent;
                border-top: 8px solid #3498db;
                margin-right: 5px;
            }
        """)
        filter_layout.addRow(project_label, self.projects_project_combo)

        # فلتر حالة المشروع مع تصميم محسن
        status_label = QLabel("📊 حالة المشروع:")
        status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)

        self.projects_status_combo = QComboBox()
        self.projects_status_combo.addItem("جميع الحالات", None)
        self.projects_status_combo.addItem("قيد التخطيط", "planning")
        self.projects_status_combo.addItem("قيد التنفيذ", "in_progress")
        self.projects_status_combo.addItem("مكتمل", "completed")
        self.projects_status_combo.addItem("متوقف", "on_hold")
        self.projects_status_combo.addItem("ملغي", "cancelled")
        self.projects_status_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #e67e22;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QComboBox:hover {
                border: 2px solid #d35400;
                background-color: #f8f9fa;
            }
            QComboBox:focus {
                border: 2px solid #1abc9c;
                background-color: #ffffff;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 5px solid transparent;
                border-top: 8px solid #e67e22;
                margin-right: 5px;
            }
        """)
        filter_layout.addRow(status_label, self.projects_status_combo)

        # أزرار التحكم مطابقة لباقي البرنامج
        buttons_layout = QHBoxLayout()

        self.projects_apply_button = QPushButton("🏗️ تطبيق التقرير")
        self.style_advanced_button(self.projects_apply_button, 'info')
        self.projects_apply_button.clicked.connect(self.generate_projects_report)

        self.projects_export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.projects_export_button, 'info')

        # إنشاء قائمة منسدلة للتصدير
        projects_export_menu = QMenu(self)
        projects_export_menu.setStyleSheet("QMenu { background-color: white; border: 1px solid #E2E8F0; }")

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(lambda: self.export_to_csv(self.projects_table, "تقرير_المشاريع"))
        projects_export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(lambda: self.print_table(self.projects_table, "تقرير المشاريع"))
        projects_export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(lambda: self.export_to_csv(self.projects_table, "تقرير_المشاريع"))
        projects_export_menu.addAction(csv_action)

        self.projects_export_button.setMenu(projects_export_menu)

        self.projects_refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.projects_refresh_button, 'modern_teal')
        self.projects_refresh_button.clicked.connect(self.generate_projects_report)

        buttons_layout.addWidget(self.projects_apply_button)
        buttons_layout.addWidget(self.projects_export_button)
        buttons_layout.addWidget(self.projects_refresh_button)
        buttons_layout.addStretch()

        filter_layout.addRow("", buttons_layout)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير مع تصميم محسن
        results_group = QGroupBox("🏗️ نتائج تقرير المشاريع")
        results_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 3px solid #3498db;
                border-radius: 15px;
                margin-top: 10px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e3f2fd,
                    stop:1 #bbdefb);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 5px 15px;
                background-color: #3498db;
                color: white;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        results_layout = QVBoxLayout()
        results_layout.setContentsMargins(20, 25, 20, 15)
        results_layout.setSpacing(15)

        # ملخص المشاريع مع تصميم محسن
        summary_layout = QHBoxLayout()
        summary_layout.setSpacing(20)

        # عدد المشاريع
        self.projects_count_label = QLabel("🏗️ عدد المشاريع: 0")
        self.projects_count_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3498db,
                    stop:1 #2980b9);
                color: white;
                border-radius: 12px;
                padding: 15px 20px;
                font-size: 14px;
                font-weight: bold;
                border: 2px solid #2980b9;
            }
        """)

        # إجمالي الميزانية
        self.projects_budget_label = QLabel("💰 إجمالي الميزانية: 0.00")
        self.projects_budget_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #e67e22,
                    stop:1 #d35400);
                color: white;
                border-radius: 12px;
                padding: 15px 20px;
                font-size: 14px;
                font-weight: bold;
                border: 2px solid #d35400;
            }
        """)

        summary_layout.addWidget(self.projects_count_label)
        summary_layout.addWidget(self.projects_budget_label)
        summary_layout.addStretch()

        # جدول المشاريع مع تصميم متطور
        self.projects_table = QTableWidget()
        self.projects_table.setColumnCount(6)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "🏗️ اسم المشروع",
            "👨‍💼 العميل",
            "🚀 تاريخ البداية",
            "🏁 تاريخ النهاية",
            "💰 الميزانية",
            "🎯 الحالة"
        ]
        self.projects_table.setHorizontalHeaderLabels(headers)

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للفواتير والمصروفات والإيرادات
        self.projects_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للفواتير والمصروفات والإيرادات
        header = self.projects_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للفواتير والمصروفات والإيرادات
        self.projects_table.verticalHeader().setDefaultSectionSize(45)
        self.projects_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        self.projects_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.projects_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.projects_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.projects_table.setAlternatingRowColors(True)

        results_layout.addLayout(summary_layout)
        results_layout.addWidget(self.projects_table)

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        self.projects_tab.setLayout(layout)

    def generate_projects_report(self):
        """إنتاج تقرير المشاريع"""
        try:
            # الحصول على الفلاتر
            project_id = self.projects_project_combo.currentData()
            status = self.projects_status_combo.currentData()

            # بناء الاستعلام
            query = self.session.query(Project)

            # تطبيق فلتر المشروع
            if project_id:
                query = query.filter(Project.id == project_id)

            # تطبيق فلتر الحالة
            if status:
                query = query.filter(Project.status == status)

            # تنفيذ الاستعلام
            projects = query.order_by(Project.name).all()

            # حساب الإحصائيات
            total_budget = sum(project.budget or 0 for project in projects)

            # تحديث الملخص
            self.projects_count_label.setText(f"🏗️ عدد المشاريع: {len(projects)}")
            self.projects_budget_label.setText(f"💰 إجمالي الميزانية: {format_currency(total_budget)}")

            # تحديث الجدول
            self.projects_table.setRowCount(len(projects))
            for row, project in enumerate(projects):
                # اسم المشروع
                self.projects_table.setItem(row, 0, QTableWidgetItem(project.name or ""))

                # العميل
                client_name = ""
                if project.client_id:
                    try:
                        client = self.session.query(Client).get(project.client_id)
                        if client:
                            client_name = client.name
                    except:
                        pass
                self.projects_table.setItem(row, 1, QTableWidgetItem(client_name))

                # تاريخ البداية
                start_date_str = project.start_date.strftime("%Y-%m-%d") if project.start_date else ""
                self.projects_table.setItem(row, 2, QTableWidgetItem(start_date_str))

                # تاريخ النهاية
                end_date_str = project.end_date.strftime("%Y-%m-%d") if project.end_date else ""
                self.projects_table.setItem(row, 3, QTableWidgetItem(end_date_str))

                # الميزانية
                self.projects_table.setItem(row, 4, QTableWidgetItem(format_currency(project.budget or 0)))

                # الحالة
                statuses = {
                    'planning': 'قيد التخطيط',
                    'in_progress': 'قيد التنفيذ',
                    'completed': 'مكتمل',
                    'on_hold': 'متوقف',
                    'cancelled': 'ملغي'
                }
                status_text = statuses.get(project.status, project.status or "")
                self.projects_table.setItem(row, 5, QTableWidgetItem(status_text))

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير المشاريع: {str(e)}")

    def export_suppliers_to_pdf(self):
        """تصدير تقرير الموردين إلى PDF"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtWidgets import QTextDocument

            printer = QPrinter()
            print_dialog = QPrintDialog(printer, self)

            if print_dialog.exec_() == QPrintDialog.Accepted:
                # الحصول على محتوى التقرير
                html_content = self.suppliers_summary.toHtml()

                # إنشاء مستند للطباعة
                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)

                QMessageBox.information(self, "نجحت الطباعة", "تم تصدير تقرير الموردين بنجاح")

        except Exception as e:
            show_error_message("خطأ في التصدير", f"حدث خطأ أثناء تصدير تقرير الموردين: {str(e)}")

    def save_suppliers_html(self):
        """حفظ تقرير الموردين كملف HTML"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ تقرير الموردين",
                "تقرير_الموردين.html",
                "HTML Files (*.html)"
            )

            if file_path:
                html_content = self.suppliers_summary.toHtml()
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)

                QMessageBox.information(self, "نجح الحفظ", f"تم حفظ تقرير الموردين بنجاح في:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ في الحفظ", f"حدث خطأ أثناء حفظ تقرير الموردين: {str(e)}")

    def export_to_csv(self, table, filename):
        """تصدير جدول إلى ملف CSV"""
        try:
            # فتح حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ التقرير",
                f"{filename}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة رؤوس الأعمدة
                    headers = []
                    for col in range(table.columnCount()):
                        headers.append(table.horizontalHeaderItem(col).text())
                    writer.writerow(headers)

                    # كتابة البيانات
                    for row in range(table.rowCount()):
                        row_data = []
                        for col in range(table.columnCount()):
                            item = table.item(row, col)
                            row_data.append(item.text() if item else "")
                        writer.writerow(row_data)

                QMessageBox.information(self, "نجح التصدير", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ في التصدير", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")

    def print_table(self, table, title):
        """طباعة جدول"""
        try:
            printer = QPrinter()
            print_dialog = QPrintDialog(printer, self)

            if print_dialog.exec_() == QPrintDialog.Accepted:
                # إنشاء HTML للطباعة
                html = f"""
                <html>
                <head>
                    <meta charset="utf-8">
                    <style>
                        body {{ font-family: Arial, sans-serif; direction: rtl; }}
                        h1 {{ text-align: center; color: #2c3e50; }}
                        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                        th, td {{ border: 1px solid #bdc3c7; padding: 8px; text-align: center; }}
                        th {{ background-color: #34495e; color: white; }}
                        tr:nth-child(even) {{ background-color: #f8f9fa; }}
                    </style>
                </head>
                <body>
                    <h1>{title}</h1>
                    <table>
                        <tr>
                """

                # إضافة رؤوس الأعمدة
                for col in range(table.columnCount()):
                    header = table.horizontalHeaderItem(col).text()
                    html += f"<th>{header}</th>"
                html += "</tr>"

                # إضافة البيانات
                for row in range(table.rowCount()):
                    html += "<tr>"
                    for col in range(table.columnCount()):
                        item = table.item(row, col)
                        cell_text = item.text() if item else ""
                        html += f"<td>{cell_text}</td>"
                    html += "</tr>"

                html += """
                    </table>
                </body>
                </html>
                """

                # طباعة HTML
                from PyQt5.QtWidgets import QTextDocument
                document = QTextDocument()
                document.setHtml(html)
                document.print_(printer)

                QMessageBox.information(self, "نجحت الطباعة", "تم إرسال التقرير للطباعة بنجاح")

        except Exception as e:
            show_error_message("خطأ في الطباعة", f"حدث خطأ أثناء طباعة التقرير: {str(e)}")

    def style_advanced_combobox(self, combobox, color_type='primary'):
        """تطبيق تصميم متطور وموحد على القوائم المنسدلة مطابق للفواتير والمصروفات والإيرادات"""
        try:
            # تحديد الألوان حسب النوع
            color_schemes = {
                'primary': {
                    'border': '#4f46e5', 'hover_border': '#6366f1', 'focus_border': '#7c3aed',
                    'bg': '#ffffff', 'hover_bg': '#f8fafc', 'focus_bg': '#ffffff',
                    'text': '#1e293b', 'arrow': '#4f46e5'
                },
                'emerald': {
                    'border': '#10b981', 'hover_border': '#14b8a6', 'focus_border': '#0d9488',
                    'bg': '#ffffff', 'hover_bg': '#f0fdfa', 'focus_bg': '#ffffff',
                    'text': '#1e293b', 'arrow': '#10b981'
                },
                'danger': {
                    'border': '#dc2626', 'hover_border': '#ef4444', 'focus_border': '#f87171',
                    'bg': '#ffffff', 'hover_bg': '#fef2f2', 'focus_bg': '#ffffff',
                    'text': '#1e293b', 'arrow': '#dc2626'
                },
                'info': {
                    'border': '#0ea5e9', 'hover_border': '#38bdf8', 'focus_border': '#7dd3fc',
                    'bg': '#ffffff', 'hover_bg': '#f0f9ff', 'focus_bg': '#ffffff',
                    'text': '#1e293b', 'arrow': '#0ea5e9'
                },
                'warning': {
                    'border': '#f59e0b', 'hover_border': '#fbbf24', 'focus_border': '#fcd34d',
                    'bg': '#ffffff', 'hover_bg': '#fffbeb', 'focus_bg': '#ffffff',
                    'text': '#1e293b', 'arrow': '#f59e0b'
                }
            }

            colors = color_schemes.get(color_type, color_schemes['primary'])

            style = f"""
                QComboBox {{
                    background: {colors['bg']};
                    border: 3px solid {colors['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-size: 14px;
                    font-weight: 600;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    color: {colors['text']};
                    min-height: 35px;
                    max-height: 35px;
                    text-align: center;
                    selection-background-color: rgba(79, 70, 229, 0.2);
                    selection-color: #ffffff;
                }}
                QComboBox:hover {{
                    background: {colors['hover_bg']};
                    border: 4px solid {colors['hover_border']};
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                }}
                QComboBox:focus {{
                    background: {colors['focus_bg']};
                    border: 4px solid {colors['focus_border']};
                    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
                }}
                QComboBox::drop-down {{
                    border: none;
                    width: 35px;
                    border-radius: 0 16px 16px 0;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(79, 70, 229, 0.1),
                        stop:1 rgba(79, 70, 229, 0.2));
                }}
                QComboBox::down-arrow {{
                    image: none;
                    border: 6px solid transparent;
                    border-top: 10px solid {colors['arrow']};
                    margin-right: 8px;
                    margin-top: 2px;
                }}
                QComboBox::down-arrow:hover {{
                    border-top: 10px solid {colors['hover_border']};
                }}
                QComboBox QAbstractItemView {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #ffffff, stop:0.2 #f8fafc, stop:0.5 #f1f5f9,
                        stop:0.8 #e2e8f0, stop:1 #cbd5e1);
                    border: none;
                    border-radius: 12px;
                    selection-background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #3B82F6, stop:0.3 #6366F1, stop:0.6 #8B5CF6, stop:1 #A855F7);
                    selection-color: #ffffff;
                    font-size: 16px;
                    font-weight: 600;
                    color: #1e293b;
                    padding: 8px;
                    outline: none;
                    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                }}
                QComboBox QAbstractItemView::item {{
                    padding: 10px 15px;
                    border-radius: 8px;
                    margin: 2px;
                    color: #1e293b;
                    font-size: 16px;
                    font-weight: 600;
                    background: transparent;
                }}
                QComboBox QAbstractItemView::item:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #ddd6fe, stop:0.3 #c4b5fd, stop:0.7 #a78bfa, stop:1 #8b5cf6);
                    color: #ffffff;
                    font-weight: 700;
                    font-size: 16px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
                    border-radius: 10px;
                }}
                QComboBox QAbstractItemView::item:selected {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #3B82F6, stop:0.3 #6366F1, stop:0.6 #8B5CF6, stop:1 #A855F7);
                    color: #ffffff;
                    font-weight: 800;
                    font-size: 16px;
                    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 10px;
                }}
            """

            combobox.setStyleSheet(style)

        except Exception as e:
            print(f"خطأ في تطبيق تصميم ComboBox: {e}")

    def apply_unified_combobox_styles(self):
        """تطبيق التصميم الموحد على جميع القوائم المنسدلة في التقارير"""
        try:
            # قوائم المشتريات
            if hasattr(self, 'purchases_period_combo'):
                self.style_advanced_combobox(self.purchases_period_combo, 'danger')
            if hasattr(self, 'purchases_supplier_combo'):
                self.style_advanced_combobox(self.purchases_supplier_combo, 'emerald')
            if hasattr(self, 'purchases_status_combo'):
                self.style_advanced_combobox(self.purchases_status_combo, 'warning')

            # قوائم المخزون
            if hasattr(self, 'inventory_category_combo'):
                self.style_advanced_combobox(self.inventory_category_combo, 'primary')
            if hasattr(self, 'inventory_supplier_combo'):
                self.style_advanced_combobox(self.inventory_supplier_combo, 'emerald')
            if hasattr(self, 'inventory_status_combo'):
                self.style_advanced_combobox(self.inventory_status_combo, 'danger')

            # قوائم العملاء
            if hasattr(self, 'clients_client_combo'):
                self.style_advanced_combobox(self.clients_client_combo, 'emerald')

            # قوائم الموردين
            if hasattr(self, 'suppliers_supplier_combo'):
                self.style_advanced_combobox(self.suppliers_supplier_combo, 'emerald')

            # قوائم المصروفات
            if hasattr(self, 'expenses_period_combo'):
                self.style_advanced_combobox(self.expenses_period_combo, 'danger')
            if hasattr(self, 'expenses_category_combo'):
                self.style_advanced_combobox(self.expenses_category_combo, 'warning')

            # قوائم الإيرادات
            if hasattr(self, 'revenues_period_combo'):
                self.style_advanced_combobox(self.revenues_period_combo, 'emerald')
            if hasattr(self, 'revenues_category_combo'):
                self.style_advanced_combobox(self.revenues_category_combo, 'warning')

            # قوائم المشاريع
            if hasattr(self, 'projects_project_combo'):
                self.style_advanced_combobox(self.projects_project_combo, 'primary')
            if hasattr(self, 'projects_status_combo'):
                self.style_advanced_combobox(self.projects_status_combo, 'warning')

            # قوائم الأرباح والخسائر
            if hasattr(self, 'profit_loss_period_combo'):
                self.style_advanced_combobox(self.profit_loss_period_combo, 'warning')

        except Exception as e:
            print(f"خطأ في تطبيق التصميم الموحد للقوائم المنسدلة: {e}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر مطابق تماماً للفواتير
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#047857', 'pressed_border': '#065f46',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'hover_border': '#dc2626', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.6)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0369a1', 'bg_bottom': '#0284c7',
                    'hover_start': '#0284c7', 'hover_mid': '#0ea5e9', 'hover_end': '#38bdf8', 'hover_bottom': '#7dd3fc',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0369a1', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.6)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#0d9488',
                    'hover_start': '#0d9488', 'hover_mid': '#14b8a6', 'hover_end': '#2dd4bf', 'hover_bottom': '#5eead4',
                    'hover_border': '#14b8a6', 'pressed_start': '#022c22', 'pressed_mid': '#042f2e',
                    'pressed_end': '#134e4a', 'pressed_bottom': '#0f766e', 'pressed_border': '#134e4a',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.6)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#4338ca',
                    'hover_start': '#4338ca', 'hover_mid': '#6366f1', 'hover_end': '#818cf8', 'hover_bottom': '#a5b4fc',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4338ca', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.6)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#0891b2',
                    'hover_start': '#0891b2', 'hover_mid': '#06b6d4', 'hover_end': '#22d3ee', 'hover_bottom': '#67e8f9',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.6)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#be185d',
                    'hover_start': '#be185d', 'hover_mid': '#ec4899', 'hover_end': '#f472b6', 'hover_bottom': '#f9a8d4',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.6)'
                },
                'orange': {
                    'bg_start': '#431407', 'bg_mid': '#7c2d12', 'bg_end': '#9a3412', 'bg_bottom': '#c2410c',
                    'hover_start': '#c2410c', 'hover_mid': '#ea580c', 'hover_end': '#f97316', 'hover_bottom': '#fb923c',
                    'hover_border': '#ea580c', 'pressed_start': '#431407', 'pressed_mid': '#7c2d12',
                    'pressed_end': '#9a3412', 'pressed_bottom': '#c2410c', 'pressed_border': '#9a3412',
                    'border': '#ea580c', 'text': '#ffffff', 'shadow': 'rgba(234, 88, 12, 0.6)'
                },
                'warning': {
                    'bg_start': '#451a03', 'bg_mid': '#78350f', 'bg_end': '#92400e', 'bg_bottom': '#b45309',
                    'hover_start': '#b45309', 'hover_mid': '#d97706', 'hover_end': '#f59e0b', 'hover_bottom': '#fbbf24',
                    'hover_border': '#d97706', 'pressed_start': '#451a03', 'pressed_mid': '#78350f',
                    'pressed_end': '#92400e', 'pressed_bottom': '#b45309', 'pressed_border': '#92400e',
                    'border': '#d97706', 'text': '#ffffff', 'shadow': 'rgba(217, 119, 6, 0.6)'
                }
            }

            # الحصول على ألوان الزر
            color_scheme = colors.get(button_type, colors['primary'])

            # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة
            menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else ""

            # تطبيق التصميم المتطور مطابق تماماً للفواتير والمصروفات والإيرادات
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']};
                    letter-spacing: 1px;
                    text-transform: uppercase;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 5px solid {color_scheme['hover_border']};
                    transform: translateY(-3px) scale(1.02);
                    box-shadow: 0 10px 25px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 30px {color_scheme['shadow']};
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                               1px 1px 3px rgba(0, 0, 0, 0.7);
                    letter-spacing: 1.2px;
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px) scale(0.98);
                    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.6),
                               0 3px 6px {color_scheme['shadow']},
                               inset 0 0 15px rgba(0, 0, 0, 0.4);
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 1.0),
                               1px 1px 2px rgba(0, 0, 0, 0.8);
                    letter-spacing: 0.8px;
                }}
                QPushButton:disabled {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #9ca3af, stop:0.5 #6b7280, stop:1 #4b5563);
                    color: #d1d5db;
                    border: 3px solid #6b7280;
                    box-shadow: none;
                    text-shadow: none;
                    text-transform: none;
                }}
                {menu_indicator}
            """

            button.setStyleSheet(style)
            print(f"✅ تم تطبيق التصميم المتطور على الزر: {button.text()} - نوع: {button_type}")

        except Exception as e:
            print(f"❌ خطأ في تطبيق تصميم الزر: {str(e)}")



    def export_to_csv(self, table, filename):
        """تصدير جدول إلى ملف CSV"""
        try:
            # فتح حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ التقرير",
                f"{filename}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة رؤوس الأعمدة
                    headers = []
                    for col in range(table.columnCount()):
                        headers.append(table.horizontalHeaderItem(col).text())
                    writer.writerow(headers)

                    # كتابة البيانات
                    for row in range(table.rowCount()):
                        row_data = []
                        for col in range(table.columnCount()):
                            item = table.item(row, col)
                            row_data.append(item.text() if item else "")
                        writer.writerow(row_data)

                QMessageBox.information(self, "نجح التصدير", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ في التصدير", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")

    def refresh_all_reports(self):
        """تحديث جميع التقارير"""
        try:
            # تحديث تقرير المبيعات
            self.generate_sales_report()
            # تحديث تقرير المشتريات
            self.generate_purchases_report()
            # تحديث تقرير الأرباح والخسائر
            self.generate_profit_loss_report()
            # تحديث تقرير المخزون
            self.generate_inventory_report()
            # تحديث تقرير العملاء
            self.generate_clients_report()

            QMessageBox.information(self, "تم التحديث", "تم تحديث جميع التقارير بنجاح")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تحديث التقارير: {str(e)}")

    def export_profit_loss_to_pdf(self):
        """تصدير تقرير الأرباح والخسائر إلى PDF"""
        try:
            from PyQt5.QtWidgets import QTextDocument
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

            printer = QPrinter()
            print_dialog = QPrintDialog(printer, self)

            if print_dialog.exec_() == QPrintDialog.Accepted:
                document = QTextDocument()
                document.setHtml(self.profit_loss_summary.toHtml())
                document.print_(printer)

                QMessageBox.information(self, "نجحت الطباعة", "تم تصدير تقرير الأرباح والخسائر إلى PDF بنجاح")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")

    def save_profit_loss_html(self):
        """حفظ تقرير الأرباح والخسائر كملف HTML"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ التقرير",
                "تقرير_الأرباح_والخسائر.html",
                "HTML Files (*.html)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.profit_loss_summary.toHtml())

                QMessageBox.information(self, "تم الحفظ", f"تم حفظ التقرير بنجاح في:\n{file_path}")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء حفظ التقرير: {str(e)}")
